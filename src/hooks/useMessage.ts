import { message } from "antd";

interface MessageInstance {
  success: (content: string, duration?: number) => void;
  error: (content: string, duration?: number) => void;
  warning: (content: string, duration?: number) => void;
  info: (content: string, duration?: number) => void;
  loading: (content: string, duration?: number) => void;
}

class GlobalMessage {
  private static instance: GlobalMessage;
  private messageApi: any = null;

  private constructor() {}

  public static getInstance(): GlobalMessage {
    if (!GlobalMessage.instance) {
      GlobalMessage.instance = new GlobalMessage();
    }
    return GlobalMessage.instance;
  }

  public setMessageApi(api: any) {
    this.messageApi = api;
  }

  public success(content: string, duration?: number) {
    if (this.messageApi) {
      this.messageApi.success({
        content,
        duration: duration || 3,
      });
    } else {
      message.success(content, duration || 3);
    }
  }

  public error(content: string, duration?: number) {
    if (this.messageApi) {
      this.messageApi.error({
        content,
        duration: duration || 3,
      });
    } else {
      message.error(content, duration || 3);
    }
  }

  public warning(content: string, duration?: number) {
    if (this.messageApi) {
      this.messageApi.warning({
        content,
        duration: duration || 3,
      });
    } else {
      message.warning(content, duration || 3);
    }
  }

  public info(content: string, duration?: number) {
    if (this.messageApi) {
      this.messageApi.info({
        content,
        duration: duration || 3,
      });
    } else {
      message.info(content, duration || 3);
    }
  }

  public loading(content: string, duration?: number) {
    if (this.messageApi) {
      return this.messageApi.loading({
        content,
        duration: duration || 0,
      });
    } else {
      return message.loading(content, duration || 0);
    }
  }
}

export const globalMessage = GlobalMessage.getInstance();

export const useMessage = (): MessageInstance => {
  return {
    success: (content: string, duration?: number) =>
      globalMessage.success(content, duration),
    error: (content: string, duration?: number) =>
      globalMessage.error(content, duration),
    warning: (content: string, duration?: number) =>
      globalMessage.warning(content, duration),
    info: (content: string, duration?: number) =>
      globalMessage.info(content, duration),
    loading: (content: string, duration?: number) =>
      globalMessage.loading(content, duration),
  };
};
