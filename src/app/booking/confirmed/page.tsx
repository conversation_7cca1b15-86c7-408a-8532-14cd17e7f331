"use client";

import ResponsiveLayout from "@/components/ResponsiveLayout";
import { useBookingStore } from "@/lib/store/booking";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import FallingStars from "@/components/FallingStars";
import MobileBookingConfirmed from "./MobileBookingConfirmed";
import DesktopBookingConfirmed from "./DesktopBookingConfirmed";

export default function BookingConfirmed() {
  const { data } = useBookingStore();
  const router = useRouter();

  // If no booking data, redirect to home
  useEffect(() => {
    if (!data.service || !data.time || !data.firstName) {
      router.push("/");
    }
  }, [data, router]);

  if (!data.service || !data.time || !data.firstName) {
    return null;
  }

  return (
    <div>
      {/* Falling Stars Animation */}
      <FallingStars />
      <ResponsiveLayout
        mobileComponent={<MobileBookingConfirmed />}
        desktopComponent={<DesktopBookingConfirmed />}
      />
    </div>
  );
}
