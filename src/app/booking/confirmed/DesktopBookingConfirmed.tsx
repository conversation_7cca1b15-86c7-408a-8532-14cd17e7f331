"use client";

import { useBookingStore } from "@/lib/store/booking";
import { useTheme } from "@/hooks/useTheme";
import { useRouter } from "next/navigation";
import { FaCheckCircle, FaCalendarAlt } from "react-icons/fa";
import { IoMdTime, IoIosTimer } from "react-icons/io";
import { FiUser } from "react-icons/fi";
import { CiViewList } from "react-icons/ci";
// import { IoWarning } from "react-icons/io5";

export default function DesktopBookingConfirmed() {
  const { data, reset } = useBookingStore();
  const { isDark } = useTheme();
  const router = useRouter();

  const service = data.serviceDetail;
  const category = data.category;
  const selectedDate = data.date ? new Date(data.date) : new Date();

  const handleNewBooking = () => {
    reset();
    router.push("/booking");
  };

  const handleGoHome = () => {
    reset();
    router.push("/");
  };

  return (
    <div
      className={`min-h-screen relative ${isDark ? "bg-gray-900" : "bg-gray-50"}`}
    >
      <div className="container mx-auto px-8 py-12 relative z-20">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="relative inline-block">
              <FaCheckCircle className="w-32 h-32 text-[#007AFF] mx-auto mb-8 animate-bounce-in animate-float drop-shadow-lg" />
              <div className="absolute inset-0 w-32 h-32 mx-auto rounded-full bg-[#007AFF] opacity-20 animate-ping"></div>
            </div>
            <h1
              className={`text-5xl font-bold mb-4 ${isDark ? "text-white" : "text-gray-900"}`}
            >
              Booking Confirmed! 🎉
            </h1>
            <p
              className={`text-xl ${isDark ? "text-gray-400" : "text-gray-600"} mb-4`}
            >
              Your appointment has been successfully booked
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Customer Info */}
            <div className="lg:col-span-1">
              <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 p-8">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-10 h-10 rounded-xl bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                    <FiUser className="text-blue-600 dark:text-blue-400 text-lg" />
                  </div>
                  <h2
                    className={`text-xl font-bold ${isDark ? "text-white" : "text-gray-900"}`}
                  >
                    Customer Information
                  </h2>
                </div>

                <div className="space-y-6">
                  <div>
                    <p
                      className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"} mb-2`}
                    >
                      Full Name
                    </p>
                    <p
                      className={`text-xl font-bold ${isDark ? "text-white" : "text-gray-900"}`}
                    >
                      {data.firstName} {data.lastName}
                    </p>
                  </div>

                  <div>
                    <p
                      className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"} mb-2`}
                    >
                      Phone Number
                    </p>
                    <p
                      className={`text-lg font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
                    >
                      {data.tel}
                    </p>
                  </div>

                  {data.email && (
                    <div>
                      <p
                        className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"} mb-2`}
                      >
                        Email Address
                      </p>
                      <p
                        className={`text-lg font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
                      >
                        {data.email}
                      </p>
                    </div>
                  )}

                  {data.numberOfPeople && data.numberOfPeople > 1 && (
                    <div>
                      <p
                        className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"} mb-2`}
                      >
                        Number of People
                      </p>
                      <p
                        className={`text-lg font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
                      >
                        {data.numberOfPeople}{" "}
                        {data.numberOfPeople === 1 ? "Person" : "People"}
                      </p>
                    </div>
                  )}

                  {data.comments && (
                    <div>
                      <p
                        className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"} mb-2`}
                      >
                        Special Requests
                      </p>
                      <p
                        className={`text-sm ${isDark ? "text-gray-300" : "text-gray-700"} bg-gray-100 dark:bg-gray-700 p-3 rounded-lg`}
                      >
                        {data.comments}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Important Notice */}
              {/* <div className="mt-8 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-700 rounded-3xl p-6">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center flex-shrink-0">
                    <IoWarning className="w-4 h-4 text-amber-600 dark:text-amber-400" />
                  </div>
                  <div>
                    <p className="font-bold text-amber-800 dark:text-amber-300 mb-2">
                      What&apos;s Next?
                    </p>
                    <ul className="text-amber-700 dark:text-amber-200 text-sm leading-relaxed space-y-1">
                      <li>• Confirmation email will be sent shortly</li>
                      <li>• We&apos;ll contact you within 30 minutes</li>
                      <li>• Arrive 10 minutes before your appointment</li>
                    </ul>
                  </div>
                </div>
              </div> */}
            </div>

            {/* Right Column - Service & Appointment Details */}
            <div className="lg:col-span-2 space-y-8">
              {/* Service Information */}
              <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 p-8">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-10 h-10 rounded-xl bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                    <CiViewList className="w-6 h-6" />
                  </div>
                  <div className="flex justify-between items-center w-full">
                    <h2
                      className={`text-xl font-bold ${isDark ? "text-white" : "text-gray-900"}`}
                    >
                      Service Details
                    </h2>
                    {category?.name && (
                      <div className="inline-flex px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 mb-2">
                        {category.name}
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-800/50 rounded-2xl p-6 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3
                        className={`text-2xl font-bold ${isDark ? "text-white" : "text-gray-900"} mb-2`}
                      >
                        {service?.name || "Selected Service"}
                      </h3>
                      <p
                        className={`${isDark ? "text-gray-400" : "text-gray-600"} mb-2`}
                      >
                        {service?.description}
                      </p>
                      {service?.duration && (
                        <div className="flex items-center space-x-2">
                          <IoIosTimer
                            className={`w-4 h-4 ${isDark ? "text-gray-400" : "text-gray-500"}`}
                          />
                          <span
                            className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
                          >
                            Duration: {service.duration} minutes
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="text-right ml-6">
                      <p className="text-3xl font-bold text-[#007AFF]">
                        ${service?.price || "0"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Appointment Schedule */}
              <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 p-8">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-10 h-10 rounded-xl bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                    <FaCalendarAlt className="text-purple-600 dark:text-purple-400 text-lg" />
                  </div>
                  <h2
                    className={`text-xl font-bold ${isDark ? "text-white" : "text-gray-900"}`}
                  >
                    Appointment Schedule
                  </h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Date */}
                  <div className="p-6 rounded-2xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/30 border border-blue-200 dark:border-blue-700">
                    <div className="flex items-center space-x-3 mb-3">
                      <FaCalendarAlt className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                      <span className="font-medium text-blue-700 dark:text-blue-300">
                        Date
                      </span>
                    </div>
                    <p
                      className={`text-xl font-bold ${isDark ? "text-white" : "text-gray-900"} mb-1`}
                    >
                      {selectedDate.toLocaleDateString("en-US", {
                        month: "long",
                        day: "numeric",
                        year: "numeric",
                      })}
                    </p>
                    <p className="text-blue-600 dark:text-blue-400 font-medium">
                      {selectedDate.toLocaleDateString("en-US", {
                        weekday: "long",
                      })}
                    </p>
                  </div>

                  {/* Time */}
                  <div className="p-6 rounded-2xl bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/30 border border-purple-200 dark:border-purple-700">
                    <div className="flex items-center space-x-3 mb-3">
                      <IoMdTime className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                      <span className="font-medium text-purple-700 dark:text-purple-300">
                        Time
                      </span>
                    </div>
                    <p
                      className={`text-xl font-bold ${isDark ? "text-white" : "text-gray-900"}`}
                    >
                      {data.time}
                    </p>
                    <p className="text-purple-600 dark:text-purple-400 font-medium">
                      Appointment time
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={handleNewBooking}
                  className="flex-1 py-4 px-8 rounded-2xl font-semibold text-lg transition-all duration-200 transform bg-[#007AFF] hover:bg-[#0051D5] text-white shadow-lg hover:scale-[1.02] active:scale-95"
                >
                  Book Another Appointment
                </button>
                <button
                  onClick={handleGoHome}
                  className={`
                     flex-1 py-4 px-8 rounded-2xl font-semibold text-lg transition-all duration-200 hover:scale-[1.02]
                    ${
                      isDark
                        ? "bg-gray-700 hover:bg-gray-600 text-white"
                        : "bg-gray-200 hover:bg-gray-300 text-gray-700"
                    }
                  `}
                >
                  Go to Home
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
