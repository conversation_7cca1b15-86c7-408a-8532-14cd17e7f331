"use client";

import { useBookingStore } from "@/lib/store/booking";
import { useTheme } from "@/hooks/useTheme";
import { useRouter } from "next/navigation";
import { FaCheckCircle, FaCalendarAlt } from "react-icons/fa";
import { IoMdTime } from "react-icons/io";
import { FiUser, FiPhone } from "react-icons/fi";
import { MdOutlineEmail } from "react-icons/md";
import { CiViewList } from "react-icons/ci";
import { IoIosPeople } from "react-icons/io";

export default function MobileBookingConfirmed() {
  const { data, reset } = useBookingStore();
  const { isDark } = useTheme();
  const router = useRouter();

  const service = data.serviceDetail;
  const selectedDate = data.date ? new Date(data.date) : new Date();

  const handleNewBooking = () => {
    reset();
    router.push("/booking");
  };

  const handleGoHome = () => {
    reset();
    router.push("/");
  };

  return (
    <div className="min-h-screen px-4 py-6">
      {/* Success Icon */}
      <div className="text-center mb-6">
        <div className="relative inline-block">
          <FaCheckCircle className="w-20 h-20 text-[#007AFF] mx-auto mb-4 animate-bounce-in animate-float" />
          <div className="absolute inset-0 w-20 h-20 mx-auto rounded-full bg-[#007AFF] opacity-20 animate-ping"></div>
        </div>
        <h1
          className={`text-2xl font-bold mb-2 ${isDark ? "text-white" : "text-gray-900"}`}
        >
          Booking Confirmed! 🎉
        </h1>
        <p className={`${isDark ? "text-gray-400" : "text-gray-600"} mb-2`}>
          Your appointment has been successfully booked
        </p>
      </div>

      {/* Booking Summary Card */}
      <div
        className={`
        p-5 rounded-3xl border shadow-lg mb-6
        ${isDark ? "border-gray-700 bg-gray-800" : "border-gray-200 bg-white"}
      `}
        style={{
          backgroundColor: isDark ? "#1C1C1E" : "#FFFFFF",
          border: `1px solid ${isDark ? "#38383A" : "#E5E5EA"}`,
          boxShadow: isDark
            ? "0 8px 32px rgba(0, 0, 0, 0.3)"
            : "0 8px 32px rgba(0, 0, 0, 0.06)",
        }}
      >
        <h2
          className={`text-lg font-bold mb-4 ${isDark ? "text-white" : "text-gray-900"}`}
        >
          Booking Details
        </h2>

        <div className="space-y-3">
          {/* Customer Info */}
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
              <FiUser className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p
                className={`text-xs ${isDark ? "text-gray-400" : "text-gray-600"}`}
              >
                Customer
              </p>
              <p
                className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
              >
                {data.firstName} {data.lastName}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
              <FiPhone className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p
                className={`text-xs ${isDark ? "text-gray-400" : "text-gray-600"}`}
              >
                Phone
              </p>
              <p
                className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
              >
                {data.tel}
              </p>
            </div>
          </div>

          {data.email && (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                <MdOutlineEmail className="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p
                  className={`text-xs ${isDark ? "text-gray-400" : "text-gray-600"}`}
                >
                  Email
                </p>
                <p
                  className={`font-semibold text-sm ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  {data.email}
                </p>
              </div>
            </div>
          )}

          {data.numberOfPeople && data.numberOfPeople > 1 && (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center">
                <IoIosPeople className="w-4 h-4 text-amber-600 dark:text-amber-400" />
              </div>
              <div>
                <p
                  className={`text-xs ${isDark ? "text-gray-400" : "text-gray-600"}`}
                >
                  Number of People
                </p>
                <p
                  className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  {data.numberOfPeople}{" "}
                  {data.numberOfPeople === 1 ? "Person" : "People"}
                </p>
              </div>
            </div>
          )}

          {data.comments && (
            <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-xl">
              <p
                className={`text-xs ${isDark ? "text-gray-400" : "text-gray-600"} mb-2`}
              >
                Special Requests
              </p>
              <p
                className={`text-sm ${isDark ? "text-gray-300" : "text-gray-700"}`}
              >
                {data.comments}
              </p>
            </div>
          )}

          <div className="border-t border-gray-200 dark:border-gray-700 pt-3 mt-3">
            <div className="flex items-center justify-between mb-2">
              <div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                    <CiViewList className="w-6 h-6" />
                  </div>
                  <p
                    className={`text-xs ${isDark ? "text-gray-400" : "text-gray-600"} mb-1`}
                  >
                    Service
                  </p>
                </div>
                <p
                  className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  {service?.name || "Selected Service"}
                </p>
                {service?.description && (
                  <p
                    className={`text-sm ${isDark ? "text-gray-300" : "text-gray-700"} mt-2`}
                  >
                    {service.description}
                  </p>
                )}
                {service?.duration && (
                  <p
                    className={`text-xs ${isDark ? "text-gray-400" : "text-gray-600"} mt-1`}
                  >
                    Duration: {service.duration} min
                  </p>
                )}
              </div>
              <div className="text-right">
                <span className="font-bold text-lg text-[#007AFF]">
                  ${service?.price || "0"}
                </span>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-200 dark:border-gray-700 pt-3 mt-3">
            {/* Date & Time */}
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-8 h-8 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center">
                <FaCalendarAlt className="w-4 h-4 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p
                  className={`text-xs ${isDark ? "text-gray-400" : "text-gray-600"}`}
                >
                  Date
                </p>
                <p
                  className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  {selectedDate.toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    weekday: "long",
                  })}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-pink-100 dark:bg-pink-900/30 flex items-center justify-center">
                <IoMdTime className="w-4 h-4 text-pink-600 dark:text-pink-400" />
              </div>
              <div>
                <p
                  className={`text-xs ${isDark ? "text-gray-400" : "text-gray-600"}`}
                >
                  Time
                </p>
                <p
                  className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  {data.time}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Next Steps */}
      {/* <div
        className={`
        p-4 rounded-2xl mb-6
        ${
          isDark
            ? "bg-blue-900/20 border border-blue-500/30"
            : "bg-blue-50 border border-blue-200"
        }
      `}
      >
        <h3
          className={`font-semibold mb-2 ${isDark ? "text-blue-300" : "text-blue-800"}`}
        >
          What&apos;s Next?
        </h3>
        <ul
          className={`text-sm space-y-1 ${isDark ? "text-blue-300" : "text-blue-700"}`}
        >
          <li>• We&apos;ll send you a confirmation email shortly</li>
          <li>• Our team will contact you within 30 minutes to confirm</li>
          <li>• Please arrive 10 minutes before your appointment time</li>
        </ul>
      </div> */}

      {/* Action Buttons */}
      <div className="space-y-3 pb-20">
        <button
          onClick={handleNewBooking}
          className="py-4 px-6 rounded-2xl font-semibold transition-all duration-200 transform bg-[#007AFF] hover:bg-[#0051D5] text-white shadow-lg hover:scale-[1.02] active:scale-95 w-full"
        >
          Book Another Appointment
        </button>
        <button
          onClick={handleGoHome}
          className={`
             py-4 px-6 rounded-2xl font-semibold transition-all duration-200 w-full
            ${
              isDark
                ? "bg-gray-700 hover:bg-gray-600 text-white"
                : "bg-gray-200 hover:bg-gray-300 text-gray-700"
            }
          `}
        >
          Go to Home
        </button>
      </div>
    </div>
  );
}
