"use client";

import { useCallback, useEffect, useState } from "react";
import ResponsiveLayout from "@/components/ResponsiveLayout";
import MobileHomePage from "@/components/pages/MobileHomePage";
import DesktopHomePage from "@/components/pages/DesktopHomePage";
import { getBusinessDetails } from "@/api/booking";
import { useMessage } from "@/hooks/useMessage";
import { ApiError } from "@/utils/api";
import { BusinessDetails } from "@/types";

export default function Home() {
  const message = useMessage();
  const [businessDetails, setBusinessDetails] = useState<BusinessDetails>();
  const [loading, setLoading] = useState(false);

  const fetchBusinessDetails = useCallback(async () => {
    try {
      setLoading(true);
      const { data } = await getBusinessDetails();
      setBusinessDetails(data.obj);
    } catch (error) {
      const apiError = error as ApiError;
      message.error(
        apiError.message || "The network request failed. Please try again later"
      );
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchBusinessDetails();
  }, [fetchBusinessDetails]);

  return (
    <ResponsiveLayout
      mobileComponent={
        <MobileHomePage businessDetails={businessDetails} loading={loading} />
      }
      desktopComponent={
        <DesktopHomePage businessDetails={businessDetails} loading={loading} />
      }
    />
  );
}
