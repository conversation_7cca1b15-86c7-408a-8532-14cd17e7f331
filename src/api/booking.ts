import api from "@/utils/api";

export const getBusinessDetails = async () => {
  return await api.get("/booking-api/v1/location-details");
};

export const getAvailableService = async () => {
  return await api.get("/booking-api/v1/available-services");
};

interface AvailableSlots {
  service_id: string;
  client_count: number;
  start_date: string;
  end_date: string;
}

export const getAvailableSlots = async (data: AvailableSlots) => {
  return await api.post("/booking-api/v1/available-slots/preview", data);
};

interface BookingSubmission {
  service_id: string;
  client_count: number;
  scheduled_start_date: string;
  scheduled_start_time: number;
  first_name: string;
  last_name: string;
  phone_number: string;
  email?: string;
}

export const submitBooking = async (data: BookingSubmission) => {
  return await api.post("/booking-api/v1/bookings", data);
};
