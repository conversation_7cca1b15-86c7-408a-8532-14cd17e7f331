export function formatLocation(loc: {
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zipcode?: string;
  country?: string;
}) {
  const parts: string[] = [];

  if (loc.address1) parts.push(loc.address1);
  if (loc.address2) parts.push(loc.address2);

  // city, state, zipcode 拼接在一起
  const cityStateZip = [loc.city, loc.state, loc.zipcode]
    .filter(Boolean)
    .join(" ");
  if (cityStateZip) parts.push(cityStateZip);

  if (loc.country) parts.push(loc.country);

  return parts.join(", ");
}

export function normalizePhoneNumber(phone: string): string {
  return phone.replace(/\D/g, "");
}

// Helper functions to convert API format to component format
export const convertDisplayTimeToTime = (displayTime: string): string => {
  // Convert "7:00 AM", "2:30 PM" etc. to "07:00", "14:30" format
  const [time, period] = displayTime.split(" ");
  const [hours, minutes] = time.split(":");
  let hour = parseInt(hours);

  if (period === "PM" && hour !== 12) {
    hour += 12;
  } else if (period === "AM" && hour === 12) {
    hour = 0;
  }

  return `${hour.toString().padStart(2, "0")}:${minutes}`;
};

// format phone number to (*************
export function formatPhoneNumberForDisplayNumber(phone: string): string {
  // 去掉所有非数字字符
  const digits = phone.replace(/\D/g, '');

  // 保留最后 10 位（避免用户传入带 +1 或其他前缀）
  const last10 = digits.slice(-10);

  if (last10.length !== 10) {
    throw new Error("Invalid phone number, must have 10 digits.");
  }

  // 格式化为 (XXX)-XXX-XXXX
  const areaCode = last10.slice(0, 3);
  const middle = last10.slice(3, 6);
  const last = last10.slice(6);

  return `(${areaCode})-${middle}-${last}`;
}