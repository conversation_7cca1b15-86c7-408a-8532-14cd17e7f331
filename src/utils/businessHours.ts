import { BusinessHours } from "@/types";

export interface FilteredBusinessHours {
  day: string;
  hours: string;
  isToday: boolean;
  isClosed: boolean;
}

const WEEKDAYS = [
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];

export const filterBusinessHours = (
  businessHours: BusinessHours[],
  todayWeekday?: number,
  timezone?: string
): FilteredBusinessHours[] => {
  return businessHours.map(dayHours => {
    const { weekday, working_times, is_day_off } = dayHours;
    const dayName = WEEKDAYS[weekday];
    // 使用timezone获取当前日期和星期
    const getCurrentWeekday = () => {
      if (timezone) {
        const now = new Date();
        const formatter = new Intl.DateTimeFormat('en-US', {
          timeZone: timezone,
          weekday: 'short'
        });
        const weekdayName = formatter.format(now);
        const weekdayMap: { [key: string]: number } = {
          'Sun': 0, 'Mon': 1, 'Tue': 2, 'Wed': 3, 'Thu': 4, 'Fri': 5, 'Sat': 6
        };
        return weekdayMap[weekdayName] ?? 0;
      }
      return todayWeekday ?? new Date().getDay();
    };

    const currentWeekday = getCurrentWeekday();
    const isToday = weekday === currentWeekday;

    if (is_day_off || working_times.length === 0) {
      return {
        day: dayName,
        hours: "Closed",
        isToday,
        isClosed: true,
      };
    }

    // 如果有多个工作时间段，找到最早的开始时间和最晚的结束时间
    const earliestStart = working_times.reduce((earliest, current) =>
      current.start_time < earliest.start_time ? current : earliest
    );

    const latestEnd = working_times.reduce((latest, current) =>
      current.end_time > latest.end_time ? current : latest
    );

    const hours = `${earliestStart.display_start_time} - ${latestEnd.display_end_time}`;

    return {
      day: dayName,
      hours,
      isToday,
      isClosed: false,
    };
  });
};

export const getCurrentStatus = (
  businessHours: BusinessHours[],
  todayWeekday: number,
  timezone?: string
): string => {
  // 获取当前时区的星期和时间
  const getCurrentTimeInfo = () => {
    if (timezone) {
      const now = new Date();
      
      // 获取星期
      const weekdayFormatter = new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        weekday: 'short'
      });
      const weekdayName = weekdayFormatter.format(now);
      const weekdayMap: { [key: string]: number } = {
        'Sun': 0, 'Mon': 1, 'Tue': 2, 'Wed': 3, 'Thu': 4, 'Fri': 5, 'Sat': 6
      };
      const weekday = weekdayMap[weekdayName] ?? 0;
      
      // 获取时间
      const timeFormatter = new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
      const timeString = timeFormatter.format(now);
      const [hours, minutes, seconds] = timeString.split(':').map(Number);
      const timeInSeconds = hours * 3600 + minutes * 60 + seconds;
      
      return { weekday, timeInSeconds };
    }
    
    const now = new Date();
    const weekday = now.getDay();
    const timeInSeconds = now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds();
    return { weekday, timeInSeconds };
  };

  const { weekday: currentWeekday, timeInSeconds: currentTimeInSeconds } = getCurrentTimeInfo();
  const today = businessHours.find(day => day.weekday === currentWeekday);

  if (!today || today.is_day_off || today.working_times.length === 0) {
    // 找到下一个工作日
    for (let i = 1; i <= 7; i++) {
      const nextDayWeekday = (currentWeekday + i) % 7;
      const nextDay = businessHours.find(day => day.weekday === nextDayWeekday);

      if (nextDay && !nextDay.is_day_off && nextDay.working_times.length > 0) {
        const earliestStart = nextDay.working_times.reduce(
          (earliest, current) =>
            current.start_time < earliest.start_time ? current : earliest
        );

        const dayName = WEEKDAYS[nextDayWeekday];
        const shortDayName = dayName.substring(0, 3); // Mon, Tue, etc.

        return `Closed ~ Opens ${earliestStart.display_start_time} ${shortDayName}`;
      }
    }

    return "Closed";
  }

  // 检查当前是否在营业时间内

  const isCurrentlyOpen = today.working_times.some(
    timeSlot =>
      currentTimeInSeconds >= timeSlot.start_time &&
      currentTimeInSeconds <= timeSlot.end_time
  );

  if (isCurrentlyOpen) {
    const currentSlot = today.working_times.find(
      timeSlot =>
        currentTimeInSeconds >= timeSlot.start_time &&
        currentTimeInSeconds <= timeSlot.end_time
    );
    return `Open ~ Closes ${currentSlot?.display_end_time}`;
  } else {
    // 找到今天接下来的营业时间
    const nextSlot = today.working_times.find(
      timeSlot => currentTimeInSeconds < timeSlot.start_time
    );

    if (nextSlot) {
      return `Closed ~ Opens ${nextSlot.display_start_time} Today`;
    } else {
      // 今天没有更多营业时间，找明天的
      for (let i = 1; i <= 7; i++) {
        const nextDayWeekday = (currentWeekday + i) % 7;
        const nextDay = businessHours.find(
          day => day.weekday === nextDayWeekday
        );

        if (
          nextDay &&
          !nextDay.is_day_off &&
          nextDay.working_times.length > 0
        ) {
          const earliestStart = nextDay.working_times.reduce(
            (earliest, current) =>
              current.start_time < earliest.start_time ? current : earliest
          );

          const dayName = WEEKDAYS[nextDayWeekday];
          const shortDayName = dayName.substring(0, 3);

          return `Closed ~ Opens ${earliestStart.display_start_time} ${shortDayName}`;
        }
      }

      return "Closed";
    }
  }
};
