import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from "axios";

const BASE_URL =
  process.env.NODE_ENV === "production"
    ? undefined
    : "https://shaneshop.test.pebbble.app";
// process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001/api";

const apiClient: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

apiClient.interceptors.request.use(
  config => {
    const token =
      typeof window !== "undefined" ? localStorage.getItem("token") : null;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 检查响应数据中的code字段，非0为错误
    if (response.data && typeof response.data === 'object' && response.data.code !== undefined) {
      if (response.data.code !== 0) {
        const apiError: ApiError = {
          code: response.data.code,
          message: response.data.message || "API returned error code",
          originalError: undefined,
        };
        return Promise.reject(apiError);
      }
    }
    return response;
  },
  (error: AxiosError) => {
    const customError = handleApiError(error);

    if (error.response?.status === 401) {
      if (typeof window !== "undefined") {
        localStorage.removeItem("token");
        window.location.href = "/";
      }
    }
    return Promise.reject(customError);
  }
);

const handleApiError = (error: AxiosError): ApiError => {
  if (error.response) {
    const { status, data } = error.response;
    const errorMessage =
      (data as any)?.message ||
      (data as any)?.error ||
      "The request failed. Please try again";

    return {
      code: status,
      message: errorMessage,
      originalError: error,
    };
  } else if (error.request) {
    return {
      code: 0,
      message:
        "The network connection failed. Please check the network connection",
      originalError: error,
    };
  } else {
    return {
      code: -1,
      message: error.message || "Request configuration error",
      originalError: error,
    };
  }
};

export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface ApiError {
  code: number;
  message: string;
  originalError?: AxiosError;
}

export const api = {
  get: async <T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.get(url, config);
      return response.data;
    } catch (error) {
      throw error as ApiError;
    }
  },

  post: async <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.post(url, data, config);
      return response.data;
    } catch (error) {
      throw error as ApiError;
    }
  },

  put: async <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.put(url, data, config);
      return response.data;
    } catch (error) {
      throw error as ApiError;
    }
  },

  patch: async <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.patch(url, data, config);
      return response.data;
    } catch (error) {
      throw error as ApiError;
    }
  },

  delete: async <T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.delete(url, config);
      return response.data;
    } catch (error) {
      throw error as ApiError;
    }
  },

  upload: async <T = any>(
    url: string,
    file: File | FormData,
    config?: AxiosRequestConfig,
    onUploadProgress?: (progressEvent: any) => void
  ): Promise<ApiResponse<T>> => {
    try {
      const formData = file instanceof FormData ? file : new FormData();
      if (file instanceof File) {
        formData.append("file", file);
      }

      const response = await apiClient.post(url, formData, {
        ...config,
        headers: {
          ...config?.headers,
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress,
      });
      return response.data;
    } catch (error) {
      throw error as ApiError;
    }
  },

  download: async (
    url: string,
    filename?: string,
    config?: AxiosRequestConfig
  ): Promise<void> => {
    try {
      const response = await apiClient.get(url, {
        ...config,
        responseType: "blob",
      });

      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filename || "download";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      throw error as ApiError;
    }
  },
};

export default api;
