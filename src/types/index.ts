export interface WorkingTimes {
  display_start_time: string;
  display_end_time: string;
  start_time: number;
  end_time: number;
}

export interface BusinessHours {
  weekday: number;
  working_times: WorkingTimes[];
  is_day_off: boolean;
}

export interface Today {
  weekday: number;
  working_times: WorkingTimes[];
  is_day_off: boolean;
}

export interface Location {
  address1: string;
  address2: string;
  city: string;
  country: string;
  state: string;
  zipcode: string;
}

export interface BusinessDetails {
  tenant_id: string;
  location_id: string;
  domain_slug: string;
  business_name: string;
  brand_image: string;
  brand_logo: string;
  about_us: string;
  phone_number: string;
  email: string;
  status: string;
  timezone: string;
  currency: string;
  currency_symbol: string;
  location: Location;
  business_hours: BusinessHours[];
  today: Today;
}

export interface BookingDetails {
  id?: string;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  serviceType?: string;
  appointmentDate?: string;
  appointmentTime?: string;
  notes?: string;
  status?: "pending" | "confirmed" | "cancelled" | "completed";
}

export interface Service {
  service_id: string;
  name: string;
  description?: string;
  duration: number;
  price: number;
}

export interface TimeSlot {
  time: string;
  available: boolean;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role?: "admin" | "user";
}

export interface ServiceCategory {
  category_id: string;
  name: string;
}

export interface ServiceList {
  category_id: string;
  name: string;
  services: Service[];
}
