// iOS-inspired mobile theme configuration for antd-mobile
export const mobileLightTheme = {
  '--adm-color-primary': '#007AFF', // iOS System Blue
  '--adm-color-success': '#34C759', // iOS System Green
  '--adm-color-warning': '#FF9500', // iOS System Orange
  '--adm-color-danger': '#FF3B30', // iOS System Red
  '--adm-color-info': '#5AC8FA', // iOS System Teal
  
  // Background colors
  '--adm-color-background': '#F2F2F7', // iOS background
  '--adm-color-fill': '#FFFFFF',
  '--adm-color-fill-secondary': '#F2F2F7',
  '--adm-color-fill-content': '#F2F2F7',
  
  // Text colors
  '--adm-color-text': '#1D1D1F', // iOS label primary
  '--adm-color-text-secondary': '#3C3C43', // iOS label secondary
  '--adm-color-weak': '#8E8E93', // iOS label tertiary
  '--adm-color-light': '#C7C7CC', // iOS label quaternary
  
  // Border colors
  '--adm-color-border': '#E5E5EA', // iOS separator
  '--adm-border-color': '#E5E5EA',
  
  // Typography
  '--adm-font-family': '-apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", system-ui, sans-serif',
  '--adm-font-size-main': '17px', // iOS default text size
  '--adm-font-size-heading': '20px',
  '--adm-font-size-caption': '15px',
  
  // Border radius
  '--adm-radius': '12px',
  '--adm-radius-s': '8px',
  '--adm-radius-l': '16px',
  
  // Button height
  '--adm-button-border-radius': '12px',
  '--adm-list-item-padding-lr': '16px',
};

export const mobileDarkTheme = {
  '--adm-color-primary': '#0A84FF', // iOS Dark Mode System Blue
  '--adm-color-success': '#30D158', // iOS Dark Mode System Green
  '--adm-color-warning': '#FF9F0A', // iOS Dark Mode System Orange
  '--adm-color-danger': '#FF453A', // iOS Dark Mode System Red
  '--adm-color-info': '#64D2FF', // iOS Dark Mode System Teal
  
  // Background colors for dark mode
  '--adm-color-background': '#000000', // iOS Dark primary background
  '--adm-color-fill': '#1C1C1E', // iOS Dark secondary background
  '--adm-color-fill-secondary': '#2C2C2E', // iOS Dark tertiary background
  '--adm-color-fill-content': '#1C1C1E',
  
  // Text colors for dark mode - improved contrast
  '--adm-color-text': '#FFFFFF', // iOS Dark label primary - white for max contrast
  '--adm-color-text-secondary': '#EBEBF5CC', // iOS Dark label secondary (80% opacity)
  '--adm-color-weak': '#EBEBF599', // iOS Dark label tertiary (60% opacity) - much brighter than before
  '--adm-color-light': '#EBEBF54D', // iOS Dark label quaternary (30% opacity)
  
  // Border colors for dark mode
  '--adm-color-border': '#38383A', // iOS Dark separator
  '--adm-border-color': '#38383A',
  
  // Typography
  '--adm-font-family': '-apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", system-ui, sans-serif',
  '--adm-font-size-main': '17px',
  '--adm-font-size-heading': '20px',
  '--adm-font-size-caption': '15px',
  
  // Border radius
  '--adm-radius': '12px',
  '--adm-radius-s': '8px',
  '--adm-radius-l': '16px',
  
  // Button height
  '--adm-button-border-radius': '12px',
  '--adm-list-item-padding-lr': '16px',
};