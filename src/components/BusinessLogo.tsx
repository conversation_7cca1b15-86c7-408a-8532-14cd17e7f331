"use client";

import React, { useState } from "react";
import { Image } from "antd";

interface BusinessLogoProps {
  businessName?: string;
  logoUrl?: string;
  size?: "sm" | "md" | "lg" | "xl" | "full";
  className?: string;
}

const BusinessLogo: React.FC<BusinessLogoProps> = ({
  businessName = "",
  logoUrl = "",
  size = "md",
  className = "",
}) => {
  const [imageLoadError, setImageLoadError] = useState(false);
  // 生成首字母 Logo
  const generateInitials = (name: string): string => {
    if (!name) return "";

    const words = name.trim().split(/\s+/);
    if (words.length === 1) {
      return words[0].charAt(0).toUpperCase();
    } else if (words.length >= 2) {
      return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
    }
    return name.charAt(0).toUpperCase();
  };

  // 尺寸配置
  const sizeConfig = {
    sm: {
      container: "w-12 h-12",
      text: "text-lg",
      rounded: "rounded-xl",
    },
    md: {
      container: "w-16 h-16",
      text: "text-xl",
      rounded: "rounded-2xl",
    },
    lg: {
      container: "w-20 h-20",
      text: "text-2xl",
      rounded: "rounded-3xl",
    },
    xl: {
      container: "w-28 h-28",
      text: "text-4xl",
      rounded: "rounded-3xl",
    },
    full: {
      container: "w-full h-full",
      text: "text-4xl",
      rounded: "rounded-3xl",
    },
  };

  const config = sizeConfig[size];

  // 检查是否有有效的 logo URL 且图片未加载失败
  const hasValidLogoUrl =
    logoUrl && logoUrl.trim().length > 0 && !imageLoadError;

  // 如果有 logo URL 且图片未失败，显示图片，否则显示首字母
  if (hasValidLogoUrl) {
    return (
      <div
        className={`${config.container} ${config.rounded} overflow-hidden ${className}`}
      >
        <Image
          src={logoUrl}
          alt={`${businessName} logo`}
          className="w-full h-full object-cover"
          onError={() => setImageLoadError(true)}
          preview={false}
        />
      </div>
    );
  }

  // 没有 logo URL 或图片加载失败，显示首字母
  return (
    <div
      className={`${config.container} ${config.rounded} bg-gradient-to-br from-pink-400 to-pink-600 flex items-center justify-center text-white font-bold ${config.text} shadow-lg shadow-black/20 ${className}`}
    >
      {generateInitials(businessName)}
    </div>
  );
};

export default BusinessLogo;
