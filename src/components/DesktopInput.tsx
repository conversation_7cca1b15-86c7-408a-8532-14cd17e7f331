"use client";
import React from "react";
import { Input, InputProps } from "antd";
import { useTheme } from "@/hooks/useTheme";

interface DesktopInputProps extends Omit<InputProps, "size"> {
  label?: string;
  icon?: React.ReactNode;
  error?: string;
  required?: boolean;
  iconColor?: string;
}

export default function DesktopInput({
  label,
  icon,
  error,
  required = false,
  iconColor = "blue",
  className = "",
  ...props
}: DesktopInputProps) {
  const { isDark } = useTheme();

  const getIconColorClasses = (color: string) => {
    const colors = {
      blue: "bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400",
      green:
        "bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400",
      purple:
        "bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400",
      red: "bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400",
      orange:
        "bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400",
      teal: "bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400",
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const inputClassName = `
    w-full px-5 py-4 rounded-2xl border transition-all duration-200 text-base
    ${
      error
        ? "border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
        : isDark
          ? "bg-[#1C1C1E] border-[#38383A] text-[#FFFFFF] placeholder-[#8E8E93] focus:border-[#007AFF] focus:ring-2 focus:ring-[#007AFF]/20"
          : "bg-[#FFFFFF] border-[#D1D1D6] text-[#1D1D1F] placeholder-[#8E8E93] focus:border-[#007AFF] focus:ring-2 focus:ring-[#007AFF]/20"
    }
    focus:outline-none hover:shadow-md
    ${className}
  `;

  return (
    <div>
      {/* Label with Icon */}
      {(label || icon) && (
        <div className="flex items-center mb-4">
          {icon && (
            <div
              className={`w-10 h-10 rounded-xl flex items-center justify-center mr-3 ${getIconColorClasses(iconColor)}`}
            >
              {icon}
            </div>
          )}
          {label && (
            <label
              className={`text-base font-semibold ${isDark ? "text-[#FFFFFF]" : "text-[#1D1D1F]"}`}
            >
              {label} {required && "*"}
            </label>
          )}
        </div>
      )}

      {/* Antd Input with Custom Styling */}
      <Input
        {...props}
        className={inputClassName}
        size="large"
        style={{
          backgroundColor: error ? undefined : isDark ? "#1C1C1E" : "#FFFFFF",
          borderColor: error ? "#ef4444" : isDark ? "#38383A" : "#D1D1D6",
          color: isDark ? "#FFFFFF" : "#1D1D1F",
        }}
      />

      {/* Error Message */}
      {error && <p className="text-red-500 text-sm mt-2 ml-1">{error}</p>}
    </div>
  );
}
