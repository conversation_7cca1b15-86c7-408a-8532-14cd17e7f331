import { ReactNode } from "react";

interface IProps {
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  children?: ReactNode;
}

export default function ButtonAnimation(props: IProps) {
  const {
    onClick = () => {},
    children,
    disabled = false,
    loading = false,
    className,
  } = props;

  return (
    <button
      className={`group relative inline-flex items-center justify-center px-8 py-3 text-lg font-bold transition-all duration-300 ease-in-out transform rounded-full ${
        disabled || loading
          ? "cursor-not-allowed opacity-50 scale-95"
          : "hover:scale-105 active:scale-95 text-white"
      } ${className}`}
      onClick={disabled || loading ? undefined : onClick}
      disabled={disabled || loading}
    >
      {/* Background Gradient */}
      <div
        className={`absolute inset-0 rounded-full transition-all duration-300 ${
          disabled || loading
            ? "bg-gray-400 dark:bg-gray-600"
            : "bg-gradient-to-br from-indigo-600 via-purple-500 to-pink-500 group-hover:scale-110 animate-gradient"
        }`}
      ></div>

      {/* Hover Glow Effect - only when enabled */}
      {!disabled && !loading && (
        <div className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-50 transition-opacity duration-300 bg-white blur-xl"></div>
      )}

      {/* Glitter Effect - only when enabled */}
      {!disabled && !loading && (
        <div className="absolute inset-0 overflow-hidden rounded-full">
          <div className="glitter-container">
            <div className="glitter"></div>
            <div className="glitter"></div>
            <div className="glitter"></div>
          </div>
        </div>
      )}

      {/* Border Effect - only when enabled */}
      {!disabled && !loading && (
        <div className="absolute inset-0 rounded-full border-2 border-white opacity-20 group-hover:opacity-40 group-hover:scale-105 transition-all duration-300"></div>
      )}

      {/* Wave Effect - only when enabled */}
      {!disabled && !loading && (
        <div className="absolute inset-0 rounded-full overflow-hidden">
          <div className="wave"></div>
        </div>
      )}

      <span className="relative z-10 flex items-center gap-2">
        {loading && (
          <svg
            className="w-5 h-5 animate-spin text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        )}
        <span
          className={`tracking-wider ${disabled || loading ? "text-gray-300 dark:text-gray-400" : ""}`}
        >
          {children}
        </span>
        {!disabled && !loading && (
          <>
            <svg
              viewBox="0 0 24 24"
              stroke="currentColor"
              fill="none"
              className="w-5 h-5 transform transition-transform duration-300 group-hover:translate-x-1"
            >
              <path
                d="M13 7l5 5m0 0l-5 5m5-5H6"
                strokeWidth="2"
                strokeLinejoin="round"
                strokeLinecap="round"
              ></path>
            </svg>
            <span className="absolute bottom-0 left-0 w-full h-0.5 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
          </>
        )}
      </span>
    </button>
  );
}
