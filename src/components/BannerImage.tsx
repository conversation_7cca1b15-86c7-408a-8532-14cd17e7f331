"use client";

import React, { useState } from "react";
import { Image } from "antd";

interface BannerImageProps {
  bannerUrl?: string;
  className?: string;
}

const BannerImage: React.FC<BannerImageProps> = ({
  bannerUrl = "",
  className = "",
}) => {
  const [imageLoadError, setImageLoadError] = useState(false);

  const hasValidBannerUrl =
    bannerUrl && bannerUrl.trim().length > 0 && !imageLoadError;

  if (hasValidBannerUrl) {
    return (
      <div className={`absolute inset-0 overflow-hidden ${className}`}>
        {/* 背景模糊图片 */}
        <div className="absolute inset-0">
          <Image
            src={bannerUrl}
            alt="banner background"
            className="w-full h-full object-cover blur-lg scale-110 opacity-80"
            onError={() => setImageLoadError(true)}
            preview={false}
          />
        </div>
        
        {/* 居中显示的主图片 */}
        <div className="absolute inset-0 flex items-center justify-center">
          <Image
            src={bannerUrl}
            alt="banner"
            className="max-w-full max-h-full object-contain shadow-2xl"
            onError={() => setImageLoadError(true)}
            preview={false}
          />
        </div>
        
        {/* 渐变遮罩增强视觉效果 */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/20 via-transparent to-black/30"></div>
      </div>
    );
  }

  return (
    <div className={`absolute inset-0 bg-gradient-to-br from-purple-500 via-purple-600 to-pink-500 opacity-90 ${className}`}>
    </div>
  );
};

export default BannerImage;