"use client";

import React, { useEffect } from 'react';
import { message } from 'antd';
import { globalMessage } from '@/hooks/useMessage';

interface MessageProviderProps {
  children: React.ReactNode;
}

export const MessageProvider: React.FC<MessageProviderProps> = ({ children }) => {
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    globalMessage.setMessageApi(messageApi);
  }, [messageApi]);

  return (
    <>
      {contextHolder}
      {children}
    </>
  );
};