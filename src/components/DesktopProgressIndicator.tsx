"use client";
import React from "react";
import { useTheme } from "@/hooks/useTheme";
import { FiUser, FiScissors, FiCalendar, FiCheckCircle } from "react-icons/fi";
import { FaCheck } from "react-icons/fa";

interface StepItem {
  index: number;
  title: string;
  subtitle: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface DesktopProgressIndicatorProps {
  currentStep: number;
}

const steps: StepItem[] = [
  {
    index: 0,
    title: "Personal Info",
    subtitle: "Your details",
    icon: FiUser,
  },
  {
    index: 1,
    title: "Select Service",
    subtitle: "Choose service",
    icon: FiScissors,
  },
  {
    index: 2,
    title: "Date & Time",
    subtitle: "Schedule",
    icon: FiCalendar,
  },
  {
    index: 3,
    title: "Confirm",
    subtitle: "Review & book",
    icon: FiCheckCircle,
  },
];

export default function DesktopProgressIndicator({
  currentStep,
}: DesktopProgressIndicatorProps) {
  const { isDark } = useTheme();

  return (
    <div className="sticky top-6 z-10">
      <div className="w-3/5 mx-auto px-4 py-4 bg-white/95 dark:bg-gray-900/95 border-b border-gray-200/50 dark:border-gray-700/50 rounded-3xl backdrop-blur-xl min-w-2xl">
        <div className="flex items-center justify-center">
          {/* Progress Steps */}
          <div className="flex items-center">
            {steps.map((stepItem, index) => {
              const isCompleted = currentStep > stepItem.index;
              const isCurrent = currentStep === stepItem.index;

              return (
                <div key={stepItem.index} className="flex items-center">
                  {/* Step Circle */}
                  <div
                    className={`
                    w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold
                    transition-all duration-300 ease-out
                    ${
                      isCompleted
                        ? "bg-[#007AFF] text-white"
                        : isCurrent
                          ? "bg-[#007AFF] text-white scale-110"
                          : isDark
                            ? "bg-gray-700 text-gray-400 border border-gray-600"
                            : "bg-gray-100 text-gray-500 border border-gray-300"
                    }
                  `}
                  >
                    {isCompleted ? (
                      <FaCheck className="w-3 h-3" />
                    ) : (
                      stepItem.index + 1
                    )}
                  </div>

                  {/* Step Title - Only show for current step on mobile, all on desktop */}
                  <div
                    className={`ml-2 transition-all duration-200 ${
                      isCurrent ? "opacity-100" : "opacity-60 hidden sm:block"
                    }`}
                  >
                    <div
                      className={`text-sm font-medium ${
                        isDark ? "text-white" : "text-gray-900"
                      }`}
                    >
                      {stepItem.title}
                    </div>
                  </div>

                  {/* Connector Line */}
                  {index < steps.length - 1 && (
                    <div
                      className={`
                      w-6 sm:w-12 h-px mx-3 sm:mx-4 transition-all duration-300
                      ${
                        isCompleted
                          ? "bg-[#007AFF]"
                          : isDark
                            ? "bg-gray-700"
                            : "bg-gray-300"
                      }
                    `}
                    />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
