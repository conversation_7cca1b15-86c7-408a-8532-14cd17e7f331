"use client";
import { NavBar } from "antd-mobile";
import { useRouter } from "next/navigation";
import { useTheme } from "@/hooks/useTheme";

export default function TopNavBar() {
  const router = useRouter();
  const { isDark } = useTheme();

  return (
    <div
      className="sticky top-0 z-50 transition-colors duration-200"
      // style={{
      //   backgroundColor: isDark
      //     ? "rgba(28, 28, 30, 0.95)"
      //     : "rgba(255, 255, 255, 0.95)",
      //   backdropFilter: "blur(20px)",
      //   WebkitBackdropFilter: "blur(20px)",
      //   borderBottom: `1px solid ${isDark ? "rgba(58, 58, 60, 0.3)" : "rgba(209, 209, 214, 0.3)"}`,
      //   boxShadow: isDark
      //     ? "0 1px 8px rgba(0, 0, 0, 0.2)"
      //     : "0 1px 8px rgba(0, 0, 0, 0.05)",
      // }}
    >
      <NavBar
        onBack={() => router.back()}
        style={
          {
            backgroundColor: "transparent",
            color: isDark ? "#FFFFFF" : "#1D1D1F",
            "--adm-color-text": isDark ? "#FFFFFF" : "#1D1D1F",
            "--adm-color-weak": isDark ? "#8E8E93" : "#8E8E93",
            "--adm-color-light": isDark ? "#3A3A3C" : "#F2F2F7",
          } as React.CSSProperties
        }
      />
    </div>
  );
}
