'use client';

import { useState, useEffect } from 'react';
import { Spin } from 'antd';
import Image from 'next/image';
import { useDevice } from '@/hooks/useDevice';
import { useDeviceContext } from './DeviceProvider';

interface ResponsiveLayoutProps {
  mobileComponent: React.ReactNode;
  desktopComponent: React.ReactNode;
  tabletComponent?: React.ReactNode;
}

export default function ResponsiveLayout({ 
  mobileComponent, 
  desktopComponent, 
  tabletComponent 
}: ResponsiveLayoutProps) {
  const initialDeviceInfo = useDeviceContext();
  const { isMobile, isTablet, isHydrated } = useDevice(initialDeviceInfo);
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    // Prevent flash by showing content after hydration
    setShowContent(true);
  }, []);

  // Show loading state to prevent flash
  if (!showContent) {
    // Detect system color scheme preference
    const prefersDark = typeof window !== 'undefined' && window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    // Determine background and spinner colors based on device and system theme
    const getLoadingColors = () => {
      if (initialDeviceInfo.shouldUseMobileUI) {
        // For mobile: use system preference
        return {
          backgroundColor: prefersDark ? '#000000' : '#F2F2F7',
          spinnerColor: prefersDark ? '#ffffff' : '#007AFF'
        };
      } else {
        // For desktop: keep existing logic
        return {
          backgroundColor: '#F2F2F7',
          spinnerColor: '#007AFF'
        };
      }
    };

    const { backgroundColor, spinnerColor } = getLoadingColors();

    return (
      <div 
        style={{ 
          width: '100vw', 
          height: '100vh', 
          backgroundColor,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '20px'
        }}
      >
        <Image
          src="/pb.jpg"
          alt="Logo"
          width={80}
          height={80}
          style={{
            borderRadius: '12px',
            objectFit: 'cover'
          }}
          priority
        />
        <Spin 
          size="large"
          style={{
            color: spinnerColor
          }}
        />
      </div>
    );
  }

  // Show mobile component for mobile devices
  if (isMobile) {
    return <div style={{ opacity: isHydrated ? 1 : 0.8, transition: 'opacity 0.3s ease' }}>{mobileComponent}</div>;
  }

  // Show tablet component if provided, otherwise use desktop
  if (isTablet) {
    return <div style={{ opacity: isHydrated ? 1 : 0.8, transition: 'opacity 0.3s ease' }}>{tabletComponent || desktopComponent}</div>;
  }

  // Show desktop component for desktop devices
  return <div style={{ opacity: isHydrated ? 1 : 0.8, transition: 'opacity 0.3s ease' }}>{desktopComponent}</div>;
}