"use client";
import { IoMdTime } from "react-icons/io";
import { CiPhone } from "react-icons/ci";
import { MdOutlineEmail } from "react-icons/md";
import { SlLocationPin } from "react-icons/sl";
import GoogleMap from "@/components/GoogleMap";
import { useTheme } from "@/hooks/useTheme";
import { useRouter } from "next/navigation";
import { FaAngleUp } from "react-icons/fa";
import { useState, useMemo } from "react";
import { BusinessDetails } from "@/types";
import { filterBusinessHours, getCurrentStatus } from "@/utils/businessHours";
import BusinessLogo from "@/components/BusinessLogo";
import BannerImage from "@/components/BannerImage";
import {
  formatLocation,
  formatPhoneNumberForDisplayNumber,
} from "@/utils/util";

interface IProps {
  businessDetails?: BusinessDetails;
  loading?: boolean;
}

export default function MobileHomePage(props: IProps) {
  const { businessDetails } = props;
  const { getBackgroundColor, isDark } = useTheme();
  const router = useRouter();
  const [isShowOpeningHours, setShowOpeningHours] = useState(false);

  const handleShowOpeningHousr = () => {
    setShowOpeningHours(prev => !prev);
  };

  const addressDetail = useMemo(() => {
    return businessDetails?.location
      ? formatLocation(businessDetails?.location)
      : "";
  }, [businessDetails?.location]);

  const filteredBusinessHours = useMemo(() => {
    if (!businessDetails?.business_hours) return [];

    const today = new Date().getDay(); // 0-6, Sunday to Saturday
    return filterBusinessHours(
      businessDetails.business_hours,
      today,
      businessDetails.timezone
    );
  }, [businessDetails?.business_hours, businessDetails?.timezone]);

  const currentStatus = useMemo(() => {
    if (!businessDetails?.business_hours) return "Closed";

    const today = new Date().getDay();
    return getCurrentStatus(
      businessDetails.business_hours,
      today,
      businessDetails.timezone
    );
  }, [businessDetails?.business_hours, businessDetails?.timezone]);

  return (
    <div className="relative pb-30">
      <div className="relative">
        <div className="w-full h-80 overflow-hidden rounded-br-[130px] relative">
          <BannerImage bannerUrl={businessDetails?.brand_image} />
        </div>
        <div
          className="absolute z-10 w-28 h-28 -bottom-14 left-16 overflow-hidden rounded-4xl p-2"
          style={{ backgroundColor: getBackgroundColor() }}
        >
          <BusinessLogo
            businessName={businessDetails?.business_name}
            logoUrl={businessDetails?.brand_logo}
            size="full"
          />
        </div>
      </div>
      <div className="pl-20 pr-20 mt-26">
        <div className="text-5xl font-bold mb-4 wrap-break-word">
          {businessDetails?.business_name}
        </div>
        <div className="text-sm mb-6 font-bold leading-relaxed">
          {businessDetails?.about_us}
        </div>
        <div
          className={`text-sm flex items-center ${!isShowOpeningHours && "mb-4"}`}
          onClick={handleShowOpeningHousr}
        >
          <IoMdTime className="mr-2" />
          {currentStatus}
          <FaAngleUp
            className={`ml-2 duration-200 ${isShowOpeningHours && "rotate-180"}`}
          />
        </div>
        {isShowOpeningHours && (
          <div className="text-sm mb-4 space-y-2">
            {filteredBusinessHours.map(({ day, hours, isToday, isClosed }) => (
              <div
                key={day}
                className={`flex justify-between ${
                  isToday
                    ? isDark
                      ? "text-gray-50 font-medium"
                      : "text-gray-800 font-medium"
                    : isClosed
                      ? "text-gray-400"
                      : "text-gray-400"
                }`}
              >
                <span>{day}</span>
                <span>{hours}</span>
              </div>
            ))}
            {businessDetails?.timezone && (
              <div className="text-center text-gray-500 mt-3">
                Time zone ({businessDetails.timezone})
              </div>
            )}
          </div>
        )}
        <div className="text-sm flex items-center mb-4">
          <CiPhone className="mr-2 flex-shrink-0" />
          <a
            className={`underline transition-colors duration-200 break-all ${
              isDark
                ? "!text-white hover:!text-gray-300"
                : "!text-black hover:!text-gray-800"
            }`}
            href={`tel:${businessDetails?.phone_number}`}
          >
            {businessDetails?.phone_number &&
              formatPhoneNumberForDisplayNumber(businessDetails.phone_number)}
          </a>
        </div>
        <div className="text-sm flex items-center mb-4">
          <MdOutlineEmail className="mr-2 flex-shrink-0" />
          <a
            className={`underline transition-colors duration-200 break-all ${
              isDark
                ? "!text-white hover:!text-gray-300"
                : "!text-black hover:!text-gray-800"
            }`}
            href={`mailto:${businessDetails?.email}`}
          >
            {businessDetails?.email}
          </a>
        </div>
        <div className="text-sm flex items-start mb-4 w-full">
          <SlLocationPin className="mr-2 w-5 h-5 flex-shrink-0 mt-0.5" />
          <a
            className={`underline transition-colors duration-200 min-w-0 whitespace-normal break-words leading-relaxed ${
              isDark
                ? "!text-white hover:!text-gray-300"
                : "!text-black hover:!text-gray-800"
            }`}
            href={`https://www.google.com/maps?q=${addressDetail}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            {addressDetail}
          </a>
        </div>
        <div className="w-full h-52 overflow-hidden rounded-2xl mb-4">
          <GoogleMap />
        </div>
      </div>
      <div className="pl-18 pr-18 fixed w-full bottom-10">
        <button
          className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold py-3 px-6 w-full rounded-full shadow-lg transform hover:scale-105 transition duration-300 ease-in-out"
          onClick={() => router.push("/booking")}
        >
          Book Now
        </button>
      </div>
    </div>
  );
}
