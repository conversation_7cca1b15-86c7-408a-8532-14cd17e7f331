"use client";
import { useBookingStore } from "@/lib/store/booking";
import { useTheme } from "@/hooks/useTheme";
import { FiUser } from "react-icons/fi";
import { MdOutlineEmail } from "react-icons/md";
import { LuPhone } from "react-icons/lu";
import { FaRegCommentDots } from "react-icons/fa";
import { InputNumber } from "antd";
import { IoIosPeople } from "react-icons/io";
import { useState } from "react";
import ButtonAnimation from "@/components/ButtonAnimation";
import DesktopInput from "@/components/DesktopInput";
import DesktopTextArea from "@/components/DesktopTextArea";

export default function StepUserInfo() {
  const { data, updateData, setStep } = useBookingStore();
  const { isDark } = useTheme();
  const [errors, setErrors] = useState<{
    firstName?: string;
    lastName?: string;
    tel?: string;
    email?: string;
    comments?: string;
  }>({});

  // Validation functions
  const validateName = (name: string) => {
    const nameRegex = /^[a-zA-Z\s'-]+$/;
    if (!name) return "This field is required";
    if (!nameRegex.test(name))
      return "Only letters, spaces, hyphens and apostrophes allowed";
    if (name.length > 50) return "Maximum 50 characters allowed";
    return "";
  };

  const formatPhoneNumber = (value: string) => {
    const cleaned = value.replace(/\D/g, "");
    if (cleaned.length <= 3) return cleaned;
    if (cleaned.length <= 6)
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
  };

  const validatePhoneNumber = (phone: string) => {
    const cleaned = phone.replace(/\D/g, "");
    if (!phone) return "Phone number is required";
    if (cleaned.length !== 10) return "Phone number must be 10 digits";
    if (!cleaned.match(/^[2-9][0-8][0-9][2-9][0-9]{6}$/))
      return "Invalid North American phone number format";
    return "";
  };

  const validateEmail = (email: string) => {
    if (!email) return "";
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return "Please enter a valid email address";
    return "";
  };

  const validateComments = (comments: string) => {
    if (comments && comments.length > 500)
      return "Comments must be 500 characters or less";
    return "";
  };

  // Handle input changes with validation
  const handleFirstNameChange = (value: string) => {
    updateData({ firstName: value });
    const error = validateName(value);
    setErrors(prev => ({ ...prev, firstName: error }));
  };

  const handleLastNameChange = (value: string) => {
    updateData({ lastName: value });
    const error = validateName(value);
    setErrors(prev => ({ ...prev, lastName: error }));
  };

  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value);
    updateData({ tel: formatted });
    const error = validatePhoneNumber(formatted);
    setErrors(prev => ({ ...prev, tel: error }));
  };

  const handleEmailChange = (value: string) => {
    updateData({ email: value });
    const error = validateEmail(value);
    setErrors(prev => ({ ...prev, email: error }));
  };

  const handleCommentsChange = (value: string) => {
    updateData({ comments: value });
    const error = validateComments(value);
    setErrors(prev => ({ ...prev, comments: error }));
  };

  // Required fields validation
  const isValid =
    data.firstName &&
    data.lastName &&
    data.tel &&
    !errors.firstName &&
    !errors.lastName &&
    !errors.tel &&
    !errors.email &&
    !errors.comments;

  return (
    <div className="max-w-4xl mx-auto px-8 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h2
          className={`text-4xl font-semibold mb-4 ${isDark ? "text-[#FFFFFF]" : "text-[#1D1D1F]"}`}
        >
          Personal Information
        </h2>
        <p
          className={`text-lg ${isDark ? "text-[#8E8E93]" : "text-[#8E8E93]"}`}
        >
          Please fill in your details to continue with your booking
        </p>
      </div>

      {/* Form Container */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 p-8 lg:p-12">
        {/* Form Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column */}
          <div className="space-y-8">
            {/* Name Section */}
            <div className="space-y-6">
              {/* First Name */}
              <DesktopInput
                label="First Name"
                icon={<FiUser className="text-lg" />}
                iconColor="blue"
                required
                type="text"
                placeholder="Enter your first name"
                value={data.firstName || ""}
                onChange={e => handleFirstNameChange(e.target.value)}
                maxLength={50}
                error={errors.firstName}
              />

              {/* Last Name */}
              <DesktopInput
                label="Last Name"
                icon={<FiUser className="text-lg" />}
                iconColor="green"
                required
                type="text"
                placeholder="Enter your last name"
                value={data.lastName || ""}
                onChange={e => handleLastNameChange(e.target.value)}
                maxLength={50}
                error={errors.lastName}
              />

              {/* Phone Number */}
              <DesktopInput
                label="Phone Number"
                icon={<LuPhone className="text-lg" />}
                iconColor="purple"
                required
                type="tel"
                placeholder="(*************"
                value={data.tel || ""}
                onChange={e => handlePhoneChange(e.target.value)}
                error={errors.tel}
              />
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-8">
            {/* Email */}
            <DesktopInput
              label="Email Address"
              icon={<MdOutlineEmail className="text-lg" />}
              iconColor="red"
              type="email"
              placeholder="<EMAIL>"
              value={data.email || ""}
              onChange={e => handleEmailChange(e.target.value)}
              error={errors.email}
            />

            {/* Number of People */}
            <div>
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-xl bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center mr-3">
                  <IoIosPeople className="text-orange-600 dark:text-orange-400 text-lg" />
                </div>
                <label
                  className={`text-base font-semibold ${isDark ? "text-[#FFFFFF]" : "text-[#1D1D1F]"}`}
                >
                  Number of People
                </label>
              </div>
              <div
                className={`
                px-5 py-4 rounded-2xl border
                ${
                  isDark
                    ? "bg-[#1C1C1E] border-[#38383A]"
                    : "bg-[#FFFFFF] border-[#D1D1D6]"
                }
              `}
              >
                <InputNumber
                  min={1}
                  max={10}
                  defaultValue={data.numberOfPeople || 1}
                  value={data.numberOfPeople || 1}
                  onChange={value => updateData({ numberOfPeople: value || 1 })}
                  // size="large"
                  className="w-full"
                  controls={{
                    upIcon: <span className="text-lg">+</span>,
                    downIcon: <span className="text-lg">−</span>,
                  }}
                />
              </div>
            </div>

            {/* Comments Section */}
            <DesktopTextArea
              label="Additional Comments"
              icon={<FaRegCommentDots className="text-lg" />}
              iconColor="teal"
              rows={4}
              placeholder="Any special requests or comments... (500 characters max)"
              value={data.comments || ""}
              onChange={e => handleCommentsChange(e.target.value)}
              maxLength={500}
              error={errors.comments}
              showCharCount={true}
              maxCharCount={500}
            />
          </div>
        </div>

        {/* Required Fields Notice */}
        <div
          className={`text-sm text-center mt-4 ${isDark ? "text-[#8E8E93]" : "text-[#8E8E93]"}`}
        >
          * Required fields
        </div>

        {/* Continue Button */}
        <div className="flex justify-center mt-8">
          <div className="w-full max-w-md">
            <ButtonAnimation
              onClick={() => setStep(1)}
              disabled={!isValid}
              className={`
                w-full py-4 px-8 text-lg font-semibold transition-all duration-200 rounded-2xl
                ${
                  isValid
                    ? "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-xl shadow-purple-500/25 hover:shadow-2xl hover:shadow-purple-500/30"
                    : `${
                        isDark
                          ? "bg-[#2C2C2E] text-[#8E8E93] cursor-not-allowed"
                          : "bg-[#F2F2F7] text-[#8E8E93] cursor-not-allowed"
                      }`
                }
              `}
            >
              Continue
            </ButtonAnimation>
          </div>
        </div>
      </div>
    </div>
  );
}
