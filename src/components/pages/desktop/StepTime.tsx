"use client";
import React, { useState, useEffect, useCallback } from "react";
import { useBookingStore } from "@/lib/store/booking";
import { useTheme } from "@/hooks/useTheme";
import { Calendar } from "antd";
import { CiCalendarDate } from "react-icons/ci";
import { IoMdTime, IoMdCheckmark } from "react-icons/io";
import { FiClock, FiCalendar } from "react-icons/fi";
import ButtonAnimation from "@/components/ButtonAnimation";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { getAvailableSlots } from "@/api/booking";
import { useMessage } from "@/hooks/useMessage";

interface TimeSlot {
  time: string;
  label: string;
  period: "morning" | "afternoon";
  available: boolean;
  start_time?: number; // Unix timestamp from API
  display_start_time?: string; // Display time from API
}

interface ApiTimeSlot {
  display_start_time: string;
  start_time: number;
  available: boolean;
}

interface ApiDayData {
  date: string;
  time_slots: ApiTimeSlot[];
}

// Helper functions to convert API format to component format
const convertDisplayTimeToTime = (displayTime: string): string => {
  // Convert "7:00 AM", "2:30 PM" etc. to "07:00", "14:30" format
  const [time, period] = displayTime.split(" ");
  const [hours, minutes] = time.split(":");
  let hour = parseInt(hours);

  if (period === "PM" && hour !== 12) {
    hour += 12;
  } else if (period === "AM" && hour === 12) {
    hour = 0;
  }

  return `${hour.toString().padStart(2, "0")}:${minutes}`;
};

const getPeriodFromTime = (displayTime: string): "morning" | "afternoon" => {
  return displayTime.includes("AM") ? "morning" : "afternoon";
};

export default function StepTime() {
  const { data, updateData, setStep } = useBookingStore();
  const { isDark } = useTheme();
  const message = useMessage();
  const [selectedDate, setSelectedDate] = useState<Dayjs>(dayjs());
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastFetchedDate, setLastFetchedDate] = useState<string | null>(null);

  const selectedTime = data.time;

  // Initialize with today's date if not already set
  useEffect(() => {
    if (!data.date) {
      const today = dayjs();
      setSelectedDate(today);
      updateData({ date: today.toISOString() });
    } else {
      const dateFromStore = dayjs(data.date);
      setSelectedDate(dateFromStore);
    }
  }, [data.date, updateData]);

  const isDateDisabled = (current: Dayjs) => {
    return current.isBefore(dayjs(), "day");
  };

  const formatSelectedDate = () => {
    return selectedDate.format("MMMM D, YYYY");
  };

  const isValid = selectedTime && selectedDate;

  // Get date range for API call (same date for start and end)
  const getDateRange = useCallback((date: Dayjs) => {
    const dateStr = date.format("YYYY-MM-DD");
    return {
      start_date: dateStr,
      end_date: dateStr,
    };
  }, []);

  // Fetch available slots for the selected date
  const fetchAvailableSlots = useCallback(
    async (selectedDate: Dayjs) => {
      if (!data.service || !selectedDate) {
        console.warn(
          "No service selected or no date selected, cannot fetch available slots"
        );
        return;
      }

      const dateKey = selectedDate.format("YYYY-MM-DD");

      // Prevent duplicate calls for the same date
      if (lastFetchedDate === dateKey || loading) {
        return;
      }

      try {
        setLoading(true);
        setLastFetchedDate(dateKey);
        const { start_date, end_date } = getDateRange(selectedDate);

        const { data: responseData } = await getAvailableSlots({
          service_id: data.service,
          client_count: 1,
          start_date,
          end_date,
        });

        // Process the API response to update available slots
        if (
          responseData?.obj &&
          Array.isArray(responseData.obj) &&
          responseData.obj.length > 0
        ) {
          const dayData: ApiDayData = responseData.obj[0]; // Should only have one day's data

          if (dayData.time_slots && Array.isArray(dayData.time_slots)) {
            // Convert API time slots to our format and match with default slots
            const apiSlots: TimeSlot[] = dayData.time_slots.map(apiSlot => ({
              time: convertDisplayTimeToTime(apiSlot.display_start_time),
              label: apiSlot.display_start_time,
              period: getPeriodFromTime(apiSlot.display_start_time),
              available: apiSlot.available,
              start_time: apiSlot.start_time,
              display_start_time: apiSlot.display_start_time,
            }));

            setAvailableSlots(apiSlots);
          } else {
            // No time_slots data, use default
            setAvailableSlots([]);
          }
        } else {
          // No data returned, set all slots to available
          setAvailableSlots([]);
        }
      } catch (err) {
        console.error("Failed to fetch available slots:", err);
        message.error("Failed to load available time slots");
        // Fall back to default slots on error
        setAvailableSlots([]);
      } finally {
        setLoading(false);
      }
    },
    [data.service, getDateRange, message, lastFetchedDate, loading]
  );

  // Initialize with today's date if not already set
  useEffect(() => {
    if (!data.date) {
      const today = dayjs();
      setSelectedDate(today);
      updateData({ date: today.toISOString() });
    } else {
      const dateFromStore = dayjs(data.date);
      setSelectedDate(dateFromStore);
    }
  }, [data.date, updateData]);

  // Fetch slots when service or selected date changes
  useEffect(() => {
    if (data.service && selectedDate) {
      fetchAvailableSlots(selectedDate);
    }
  }, [data.service, selectedDate, fetchAvailableSlots]);

  // Handle calendar month/year panel changes (for navigation)
  const onPanelChange = useCallback((value: Dayjs, mode: "month" | "year") => {
    console.log("Panel change:", value.format("YYYY-MM"), "mode:", mode);
    // Panel changes don't need to trigger API calls since we only fetch data for selected dates
  }, []);

  // Handle calendar date changes
  const handleCalendarChange = useCallback(
    (value: Dayjs | null) => {
      if (value) {
        console.log("Date selected:", value.format("YYYY-MM-DD"));

        // Update the selected date and reset last fetched date to allow new API call
        setSelectedDate(value);
        updateData({
          date: value.toISOString(),
          time: "",
          scheduled_start_time: undefined,
        });

        // Reset last fetched date to ensure new date gets fresh data
        setLastFetchedDate(null);
      }
    },
    [updateData]
  );

  // Create alias for calendar onChange prop
  const onDateChange = handleCalendarChange;

  return (
    <div className="max-w-6xl mx-auto px-8 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h2
          className={`text-4xl font-semibold mb-4 ${isDark ? "text-white" : "text-gray-900"}`}
        >
          Choose Date & Time
        </h2>
        <p className={`text-lg ${isDark ? "text-gray-400" : "text-gray-600"}`}>
          Select your preferred appointment date and time
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Date & Time Selection */}
        <div className="lg:col-span-2 space-y-8">
          {/* Calendar Section */}
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 p-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 rounded-xl bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                <FiCalendar className="text-blue-600 dark:text-blue-400 text-lg" />
              </div>
              <h3
                className={`text-2xl font-bold ${isDark ? "text-white" : "text-gray-900"}`}
              >
                Select Date
              </h3>
            </div>

            <div className="calendar-container">
              <Calendar
                fullscreen={false}
                value={selectedDate}
                onChange={onDateChange}
                onPanelChange={onPanelChange}
                disabledDate={isDateDisabled}
                className={`desktop-calendar ${isDark ? "dark-theme" : "light-theme"}`}
              />
            </div>
          </div>

          {/* Time Selection */}
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 p-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 rounded-xl bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                <FiClock className="text-purple-600 dark:text-purple-400 text-lg" />
              </div>
              <h3
                className={`text-2xl font-bold ${isDark ? "text-white" : "text-gray-900"}`}
              >
                Select Time
              </h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Morning Slots */}
              <div>
                <h4
                  className={`text-lg font-semibold mb-4 ${isDark ? "text-gray-300" : "text-gray-700"}`}
                >
                  Morning
                </h4>
                <div className="grid grid-cols-1 gap-3">
                  {availableSlots
                    .filter(slot => slot.period === "morning")
                    .map(slot => (
                      <button
                        key={slot.time}
                        disabled={!slot.available || loading}
                        className={`
                        relative py-3 px-4 rounded-xl font-semibold text-sm transition-all duration-200 group
                        ${
                          !slot.available
                            ? "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-400 dark:text-gray-600 cursor-not-allowed"
                            : selectedTime === slot.time
                              ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg transform scale-105"
                              : isDark
                                ? "bg-gray-700 border border-gray-600 text-gray-300 hover:bg-gray-600 hover:border-gray-500"
                                : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 hover:shadow-md"
                        }
                      `}
                        onClick={() =>
                          slot.available &&
                          updateData({
                            time: slot.time,
                            scheduled_start_time: slot.start_time,
                          })
                        }
                      >
                        <span className="flex items-center justify-center space-x-2">
                          <IoMdTime className="w-4 h-4" />
                          <span>{slot.label}</span>
                          {selectedTime === slot.time && slot.available && (
                            <IoMdCheckmark className="w-4 h-4 ml-1" />
                          )}
                          {!slot.available && (
                            <span className="text-xs ml-1">(Full)</span>
                          )}
                        </span>
                      </button>
                    ))}
                </div>
              </div>

              {/* Afternoon Slots */}
              <div>
                <h4
                  className={`text-lg font-semibold mb-4 ${isDark ? "text-gray-300" : "text-gray-700"}`}
                >
                  Afternoon
                </h4>
                <div className="grid grid-cols-1 gap-3">
                  {availableSlots
                    .filter(slot => slot.period === "afternoon")
                    .map(slot => (
                      <button
                        key={slot.time}
                        disabled={!slot.available || loading}
                        className={`
                        relative py-3 px-4 rounded-xl font-semibold text-sm transition-all duration-200 group
                        ${
                          !slot.available
                            ? "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-400 dark:text-gray-600 cursor-not-allowed"
                            : selectedTime === slot.time
                              ? "bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg transform scale-105"
                              : isDark
                                ? "bg-gray-700 border border-gray-600 text-gray-300 hover:bg-gray-600 hover:border-gray-500"
                                : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 hover:shadow-md"
                        }
                      `}
                        onClick={() =>
                          slot.available &&
                          updateData({
                            time: slot.time,
                            scheduled_start_time: slot.start_time,
                          })
                        }
                      >
                        <span className="flex items-center justify-center space-x-2">
                          <IoMdTime className="w-4 h-4" />
                          <span>{slot.label}</span>
                          {selectedTime === slot.time && slot.available && (
                            <IoMdCheckmark className="w-4 h-4 ml-1" />
                          )}
                          {!slot.available && (
                            <span className="text-xs ml-1">(Full)</span>
                          )}
                        </span>
                      </button>
                    ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Appointment Summary */}
        <div className="lg:col-span-1">
          <div className="sticky top-30">
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 p-8">
              <h3
                className={`text-xl font-bold mb-6 ${isDark ? "text-white" : "text-gray-900"}`}
              >
                Appointment Summary
              </h3>

              {isValid ? (
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 p-4 rounded-2xl bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
                    <div className="w-8 h-8 rounded-xl bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                      <CiCalendarDate className="text-green-600 dark:text-green-400 text-lg" />
                    </div>
                    <div>
                      <div
                        className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
                      >
                        {formatSelectedDate()}
                      </div>
                      <div
                        className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
                      >
                        Selected Date
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-4 rounded-2xl bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800">
                    <div className="w-8 h-8 rounded-xl bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                      <IoMdTime className="text-purple-600 dark:text-purple-400 text-lg" />
                    </div>
                    <div>
                      <div
                        className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
                      >
                        {
                          availableSlots?.find(
                            slot => slot.time === selectedTime
                          )?.label
                        }
                      </div>
                      <div
                        className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
                      >
                        Selected Time
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                    <FiClock
                      className={`text-2xl ${isDark ? "text-gray-500" : "text-gray-400"}`}
                    />
                  </div>
                  <p
                    className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
                  >
                    Select date and time to see summary
                  </p>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="space-y-4 mt-8">
                <button
                  className={`
                  w-full py-3 px-6 rounded-2xl font-semibold transition-all duration-200 hover:scale-[1.02]
                  ${
                    isDark
                      ? "bg-gray-700 hover:bg-gray-600 text-white"
                      : "bg-gray-200 hover:bg-gray-300 text-gray-700"
                  }
                `}
                  onClick={() => setStep(1)}
                >
                  Previous
                </button>

                <ButtonAnimation
                  onClick={() => setStep(3)}
                  disabled={!isValid}
                  className="w-full"
                >
                  Continue
                </ButtonAnimation>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
