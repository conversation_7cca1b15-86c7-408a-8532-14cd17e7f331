"use client";
import React, { useState, useCallback, useEffect } from "react";
import { useBookingStore } from "@/lib/store/booking";
import { useTheme } from "@/hooks/useTheme";
import { FaCheckCircle, FaRegCommentDots } from "react-icons/fa";
import { FiUser, FiScissors, FiCalendar, FiClock } from "react-icons/fi";
import { CiPhone, CiCalendarDate } from "react-icons/ci";
import { MdOutlineEmail } from "react-icons/md";
import { IoMdTime, IoIosPeople } from "react-icons/io";
import { RiPaintBrushLine } from "react-icons/ri";
import { useRouter } from "next/navigation";
// import { IoWarning } from "react-icons/io5";
import ButtonAnimation from "@/components/ButtonAnimation";
import { submitBooking } from "@/api/booking";
import { useMessage } from "@/hooks/useMessage";
import dayjs from "dayjs";
import { normalizePhoneNumber } from "@/utils/util";

const getCategoryIcon = () => {
  const list = [FiScissors, FiUser, RiPaintBrushLine];
  const randomIndex = Math.floor(Math.random() * list.length);
  return list[randomIndex];
};

const getCategoryColor = () => {
  const list = ["blue", "pruple", "pink"];
  const randomIndex = Math.floor(Math.random() * list.length);
  return list[randomIndex];
};

export default function StepConfirm() {
  const { data, setStep } = useBookingStore();
  const { isDark } = useTheme();
  const router = useRouter();
  const message = useMessage();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const service = {
    service: data.service,
    name: data.serviceDetail?.name,
    price: `$${data.serviceDetail?.price || 0}`,
    duration: `${data.serviceDetail?.duration || 0} min`,
    category: data.category?.name,
    description: data.serviceDetail?.description,
  };
  const selectedDate = data.date ? new Date(data.date) : new Date();
  const categoryIcon = getCategoryIcon();
  const categoryColor = getCategoryColor();

  // Validate booking data
  const validateBookingData = useCallback(() => {
    const errors: string[] = [];

    if (!data.firstName?.trim()) errors.push("First name is required");
    if (!data.lastName?.trim()) errors.push("Last name is required");
    if (!data.tel?.trim()) errors.push("Phone number is required");
    if (!data.service) errors.push("Service selection is required");
    if (!data.date) errors.push("Date selection is required");
    if (!data.time) errors.push("Time selection is required");

    // Validate phone format (basic)
    if (data.tel && !/^[\d\s\-\(\)\+]+$/.test(data.tel)) {
      errors.push("Please enter a valid phone number");
    }

    // Validate email format if provided
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push("Please enter a valid email address");
    }

    setValidationErrors(errors);
    return errors.length === 0;
  }, [data]);

  const handleConfirm = useCallback(async () => {
    // Validate data first
    if (!validateBookingData()) {
      message.error("Please correct the errors before submitting");
      return;
    }

    try {
      setIsSubmitting(true);

      // Prepare booking data for API
      const bookingData = {
        service_id: data.service!,
        client_count: data.numberOfPeople || 1,
        scheduled_start_date: dayjs(data.date).format("YYYY-MM-DD"),
        scheduled_start_time: data.scheduled_start_time as number,
        first_name: data.firstName!.trim(),
        last_name: data.lastName!.trim(),
        phone_number: "+1" + normalizePhoneNumber(data.tel!.trim()),
        note: data?.comments || undefined,
        email: data.email?.trim() || undefined,
      };

      console.log("Submitting booking data:", bookingData);

      const response = await submitBooking(bookingData);

      console.log("Booking submitted successfully:", response.data);

      // Navigate to confirmation page
      router.replace("/booking/confirmed");
    } catch (error: any) {
      console.error("Failed to submit booking:", error);
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to submit booking";
      message.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  }, [data, validateBookingData, message, router]);

  // Validate on component mount and data changes
  useEffect(() => {
    validateBookingData();
  }, [validateBookingData]);

  const formatTimeToAMPM = (time24: string) => {
    if (!time24) return time24;
    const [hours, minutes] = time24.split(":");
    const hour12 =
      parseInt(hours) > 12 ? parseInt(hours) - 12 : parseInt(hours);
    const ampm = parseInt(hours) >= 12 ? "PM" : "AM";
    const displayHour = hour12 === 0 ? 12 : hour12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  return (
    <div className="max-w-5xl mx-auto px-8 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <div className="relative inline-block">
          <FaCheckCircle className="w-20 h-20 text-green-500 mx-auto mb-6 drop-shadow-lg" />
          <div className="absolute inset-0 w-20 h-20 mx-auto bg-green-500/20 rounded-full animate-pulse"></div>
        </div>
        <h2
          className={`text-4xl font-semibold mb-4 ${isDark ? "text-white" : "text-gray-900"}`}
        >
          Confirm Your Booking
        </h2>
        <p className={`text-lg ${isDark ? "text-gray-400" : "text-gray-600"}`}>
          Please review the following information carefully before confirming
          your appointment
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Personal Information & Notice */}
        <div className="lg:col-span-1 space-y-6">
          {/* Personal Information */}
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 rounded-xl bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                <FiUser className="text-green-600 dark:text-green-400 text-sm" />
              </div>
              <h3
                className={`text-lg font-bold ${isDark ? "text-white" : "text-gray-900"}`}
              >
                Personal Details
              </h3>
            </div>

            <div className="space-y-3">
              {/* Name */}
              <div className="p-3 rounded-xl bg-gray-50 dark:bg-gray-700/50">
                <div className="flex items-center space-x-2">
                  <FiUser
                    className={`w-4 h-4 ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  />
                  <div className="flex-1">
                    <p
                      className={`text-xs font-medium ${isDark ? "text-gray-400" : "text-gray-600"}`}
                    >
                      Full Name
                    </p>
                    <p
                      className={`font-bold ${isDark ? "text-white" : "text-gray-900"}`}
                    >
                      {data.firstName} {data.lastName}
                    </p>
                  </div>
                </div>
              </div>

              {/* Phone */}
              <div className="p-3 rounded-xl bg-gray-50 dark:bg-gray-700/50">
                <div className="flex items-center space-x-2">
                  <CiPhone
                    className={`w-4 h-4 ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  />
                  <div className="flex-1">
                    <p
                      className={`text-xs font-medium ${isDark ? "text-gray-400" : "text-gray-600"}`}
                    >
                      Phone
                    </p>
                    <p
                      className={`font-bold ${isDark ? "text-white" : "text-gray-900"}`}
                    >
                      {data.tel}
                    </p>
                  </div>
                </div>
              </div>

              {/* Email */}
              {data.email && (
                <div className="p-3 rounded-xl bg-gray-50 dark:bg-gray-700/50">
                  <div className="flex items-center space-x-2">
                    <MdOutlineEmail
                      className={`w-4 h-4 ${isDark ? "text-gray-400" : "text-gray-500"}`}
                    />
                    <div className="flex-1">
                      <p
                        className={`text-xs font-medium ${isDark ? "text-gray-400" : "text-gray-600"}`}
                      >
                        Email
                      </p>
                      <p
                        className={`font-bold text-sm ${isDark ? "text-white" : "text-gray-900"}`}
                      >
                        {data.email}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Number of People */}
              <div className="p-3 rounded-xl bg-gray-50 dark:bg-gray-700/50">
                <div className="flex items-center space-x-2">
                  <IoIosPeople
                    className={`w-4 h-4 ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  />
                  <div className="flex-1">
                    <p
                      className={`text-xs font-medium ${isDark ? "text-gray-400" : "text-gray-600"}`}
                    >
                      People
                    </p>
                    <p
                      className={`font-bold ${isDark ? "text-white" : "text-gray-900"}`}
                    >
                      {data.numberOfPeople || 1}{" "}
                      {(data.numberOfPeople || 1) === 1 ? "Person" : "People"}
                    </p>
                  </div>
                </div>
              </div>

              {/* Comments */}
              {data.comments && (
                <div className="p-3 rounded-xl bg-gray-50 dark:bg-gray-700/50">
                  <div className="flex items-start space-x-2">
                    <FaRegCommentDots
                      className={`w-4 h-4 mt-0.5 ${isDark ? "text-gray-400" : "text-gray-500"}`}
                    />
                    <div className="flex-1">
                      <p
                        className={`text-xs font-medium ${isDark ? "text-gray-400" : "text-gray-600"}`}
                      >
                        Special Requests
                      </p>
                      <p
                        className={`font-semibold break-words text-sm ${isDark ? "text-white" : "text-gray-900"} mt-1`}
                      >
                        {data.comments}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Important Notice */}
          {/* <div className="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-700 rounded-3xl p-5">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center flex-shrink-0">
                <IoWarning className="w-3 h-3 text-amber-600 dark:text-amber-400" />
              </div>
              <div>
                <p className="font-bold text-amber-800 dark:text-amber-300 mb-1 text-sm">
                  Important Notice
                </p>
                <p className="text-amber-700 dark:text-amber-200 text-xs leading-relaxed">
                  We will contact you within 30 minutes to confirm your
                  appointment. Please contact us if you need to make changes.
                </p>
              </div>
            </div>
          </div> */}
        </div>

        {/* Right Columns - Service & Appointment Info */}
        <div className="lg:col-span-2 space-y-8">
          {/* Service Information */}
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 p-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div
                  className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                    categoryColor === "blue"
                      ? "bg-blue-100 dark:bg-blue-900/30"
                      : categoryColor === "purple"
                        ? "bg-purple-100 dark:bg-purple-900/30"
                        : "bg-pink-100 dark:bg-pink-900/30"
                  }`}
                >
                  {React.createElement(categoryIcon, {
                    className: `text-lg ${
                      categoryColor === "blue"
                        ? "text-blue-600 dark:text-blue-400"
                        : categoryColor === "purple"
                          ? "text-purple-600 dark:text-purple-400"
                          : "text-pink-600 dark:text-pink-400"
                    }`,
                  })}
                </div>
                <h3
                  className={`text-xl font-bold ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  Selected Service
                </h3>
              </div>
              <div
                className={`px-4 py-2 rounded-full text-sm font-medium ${
                  categoryColor === "blue"
                    ? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                    : categoryColor === "purple"
                      ? "bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300"
                      : "bg-pink-50 dark:bg-pink-900/20 text-pink-700 dark:text-pink-300"
                }`}
              >
                {service?.category}
              </div>
            </div>

            <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-800/50 rounded-2xl p-6 border border-gray-200 dark:border-gray-600">
              <div className="flex justify-between">
                <div>
                  <h4
                    className={`text-2xl font-bold ${isDark ? "text-white" : "text-gray-900"} mb-2`}
                  >
                    {service?.name}
                  </h4>
                  <p
                    className={`${isDark ? "text-gray-400" : "text-gray-600"} mb-2`}
                  >
                    {service?.description}
                  </p>
                  <div className="flex items-center space-x-4 text-sm">
                    <span
                      className={`flex items-center ${isDark ? "text-gray-400" : "text-gray-600"}`}
                    >
                      <FiClock className="w-4 h-4 mr-1" />
                      {service?.duration}
                    </span>
                  </div>
                </div>
                <div
                  className={`text-3xl font-bold ${
                    categoryColor === "blue"
                      ? "text-blue-600"
                      : categoryColor === "purple"
                        ? "text-purple-600"
                        : "text-pink-600"
                  }`}
                >
                  {service?.price}
                </div>
              </div>
            </div>
          </div>

          {/* Appointment Date & Time */}
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 p-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 rounded-xl bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center">
                <FiCalendar className="text-orange-600 dark:text-orange-400 text-lg" />
              </div>
              <h3
                className={`text-xl font-bold ${isDark ? "text-white" : "text-gray-900"}`}
              >
                Appointment Schedule
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Date */}
              <div className="p-6 rounded-2xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/30 border border-blue-200 dark:border-blue-700">
                <div className="flex items-center space-x-3 mb-3">
                  <CiCalendarDate className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                    Date
                  </span>
                </div>
                <p
                  className={`text-xl font-bold ${isDark ? "text-white" : "text-gray-900"} mb-1`}
                >
                  {selectedDate.toLocaleDateString("en-US", {
                    month: "long",
                    day: "numeric",
                    year: "numeric",
                  })}
                </p>
                <p className="text-blue-600 dark:text-blue-400 font-medium">
                  {selectedDate.toLocaleDateString("en-US", {
                    weekday: "long",
                  })}
                </p>
              </div>

              {/* Time */}
              <div className="p-6 rounded-2xl bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/30 border border-purple-200 dark:border-purple-700">
                <div className="flex items-center space-x-3 mb-3">
                  <IoMdTime className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                  <span className="text-sm font-medium text-purple-700 dark:text-purple-300">
                    Time
                  </span>
                </div>
                <p
                  className={`text-xl font-bold ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  {formatTimeToAMPM(data.time || "")}
                </p>
                <p className="text-purple-600 dark:text-purple-400 font-medium">
                  Duration: {service?.duration}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <button
              className={`
                flex-1 py-4 px-8 rounded-2xl font-semibold text-lg transition-all duration-200 hover:scale-[1.02]
                ${
                  isDark
                    ? "bg-gray-700 hover:bg-gray-600 text-white"
                    : "bg-gray-200 hover:bg-gray-300 text-gray-700"
                }
              `}
              onClick={() => setStep(2)}
            >
              ← Back to Date & Time
            </button>

            <div className="flex-1">
              <ButtonAnimation
                onClick={handleConfirm}
                disabled={isSubmitting || validationErrors.length > 0}
                className="w-full py-4 px-8 text-xl font-bold"
                loading={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Confirm Booking"}
              </ButtonAnimation>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
