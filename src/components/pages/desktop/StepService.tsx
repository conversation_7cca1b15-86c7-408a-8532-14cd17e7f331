"use client";
import React, { useEffect, useState, useCallback } from "react";
import { useBookingStore } from "@/lib/store/booking";
import { useTheme } from "@/hooks/useTheme";
import { FaCheck } from "react-icons/fa";
import { FiStar } from "react-icons/fi";
import { AiOutlineCalendar } from "react-icons/ai";
import { RiPaintBrushLine } from "react-icons/ri";
import { TiWarning } from "react-icons/ti";
import { IoIosTimer } from "react-icons/io";
import ButtonAnimation from "@/components/ButtonAnimation";
import { getAvailableService } from "@/api/booking";
import { ServiceList, Service, ServiceCategory } from "@/types/index";
import { useMessage } from "@/hooks/useMessage";

export default function StepService() {
  const { data, updateData, setStep } = useBookingStore();
  const { isDark } = useTheme();
  const message = useMessage();
  const [serviceData, setServiceData] = useState<ServiceList[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const selectedService = data.service;

  // Get selected service details from API data
  const getSelectedServiceDetails = useCallback(() => {
    for (const category of serviceData) {
      const service = category.services?.find(
        s => s.service_id === selectedService
      );
      if (service) {
        return { service, category };
      }
    }
    return null;
  }, [serviceData, selectedService]);

  const getAvailableServiceDetail = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const { data } = await getAvailableService();
      setServiceData(data?.obj || []);
    } catch (err) {
      console.error("getAvailableService error:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load services";
      setError(errorMessage);
      message.error("Failed to load services. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [message]);

  const handleServiceSelect = useCallback(
    (serviceId: string, service: Service, category: ServiceCategory) => {
      updateData({ service: serviceId, serviceDetail: service, category });
    },
    [updateData]
  );

  const selectedDetails = getSelectedServiceDetails();

  useEffect(() => {
    getAvailableServiceDetail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="max-w-7xl mx-auto px-8 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h2
          className={`text-4xl font-semibold mb-4 ${isDark ? "text-white" : "text-gray-900"}`}
        >
          Choose Your Service
        </h2>
        <p className={`text-lg ${isDark ? "text-gray-400" : "text-gray-600"}`}>
          Select the perfect service for your needs
        </p>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
        {/* Services Selection - Left 3 columns */}
        <div className="xl:col-span-3">
          {loading ? (
            <div className="flex items-center justify-center py-20">
              <div className="text-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent mx-auto"></div>
                <p
                  className={`text-lg ${isDark ? "text-gray-400" : "text-gray-600"}`}
                >
                  Loading services...
                </p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center py-20">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center">
                  <TiWarning  className="w-8 h-8 text-red-500" />
                </div>
                <p
                  className={`text-lg font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  Failed to load services
                </p>
                <p
                  className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
                >
                  {error}
                </p>
              </div>
            </div>
          ) : serviceData.length === 0 ? (
            <div className="flex items-center justify-center py-20">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  <AiOutlineCalendar
                    className={`text-2xl ${isDark ? "text-gray-500" : "text-gray-400"}`}
                  />
                </div>
                <p
                  className={`text-lg ${isDark ? "text-gray-400" : "text-gray-600"}`}
                >
                  No services available
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-8">
              {serviceData.map((category, categoryIndex) => {
                const categoryColor = ["blue", "purple", "pink"][
                  categoryIndex % 3
                ];
                const IconComponent = [
                  AiOutlineCalendar,
                  FiStar,
                  RiPaintBrushLine,
                ][categoryIndex % 3];

                return (
                  <div key={category.category_id} className="space-y-6">
                    {/* Category Header */}
                    <div className="flex items-center space-x-4 mb-6">
                      <div
                        className={`w-12 h-12 rounded-2xl flex items-center justify-center ${
                          categoryColor === "blue"
                            ? "bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400"
                            : categoryColor === "purple"
                              ? "bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400"
                              : "bg-pink-100 dark:bg-pink-900/30 text-pink-600 dark:text-pink-400"
                        }`}
                      >
                        <IconComponent className="text-xl" />
                      </div>
                      <h3
                        className={`text-2xl font-bold ${isDark ? "text-white" : "text-gray-900"}`}
                      >
                        {category.name}
                      </h3>
                    </div>

                    {/* Services Grid */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      {category.services?.map(service => {
                        const isSelected =
                          selectedService === service.service_id;
                        const colorClasses = {
                          bg: isSelected
                            ? categoryColor === "blue"
                              ? "bg-blue-50 dark:bg-blue-900/20"
                              : categoryColor === "purple"
                                ? "bg-purple-50 dark:bg-purple-900/20"
                                : "bg-pink-50 dark:bg-pink-900/20"
                            : "bg-white dark:bg-gray-800",
                          border: isSelected
                            ? categoryColor === "blue"
                              ? "border-blue-500"
                              : categoryColor === "purple"
                                ? "border-purple-500"
                                : "border-pink-500"
                            : "border-gray-200 dark:border-gray-700",
                          hover:
                            categoryColor === "blue"
                              ? "hover:border-blue-300 dark:hover:border-blue-600"
                              : categoryColor === "purple"
                                ? "hover:border-purple-300 dark:hover:border-purple-600"
                                : "hover:border-pink-300 dark:hover:border-pink-600",
                        };

                        return (
                          <div
                            key={service.service_id}
                            className={`
                              relative p-6 rounded-2xl border-2 cursor-pointer transition-all duration-300 group
                              ${colorClasses.bg} ${colorClasses.border} ${colorClasses.hover}
                              ${isSelected ? "shadow-xl scale-[1.02]" : "hover:shadow-lg hover:scale-[1.01]"}
                            `}
                            onClick={() =>
                              handleServiceSelect(
                                service.service_id,
                                service,
                                category
                              )
                            }
                          >
                            <div className="flex items-start justify-between gap-4">
                              <div className="flex-1 min-w-0">
                                <h4
                                  className={`font-bold text-xl mb-2 ${isDark ? "text-white" : "text-gray-900"}`}
                                >
                                  {service.name}
                                </h4>
                                <p
                                  className={`text-sm mb-3 ${isDark ? "text-gray-400" : "text-gray-600"}`}
                                >
                                  {service.description}
                                </p>
                                <div className="flex items-center space-x-4 text-sm">
                                  <span
                                    className={`flex items-center ${isDark ? "text-gray-400" : "text-gray-600"}`}
                                  >
                                    <IoIosTimer className="w-4 h-4 mr-1" />
                                    Duration: {service.duration} min
                                  </span>
                                </div>
                              </div>

                              <div className="flex flex-col items-end space-y-3">
                                <span
                                  className={`font-bold text-2xl ${
                                    isSelected
                                      ? categoryColor === "blue"
                                        ? "text-blue-600"
                                        : categoryColor === "purple"
                                          ? "text-purple-600"
                                          : "text-pink-600"
                                      : isDark
                                        ? "text-white"
                                        : "text-gray-900"
                                  }`}
                                >
                                  ${service.price}
                                </span>
                                <div
                                  className={`
                                w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-200
                                ${
                                  isSelected
                                    ? categoryColor === "blue"
                                      ? "border-blue-500 bg-blue-500"
                                      : categoryColor === "purple"
                                        ? "border-purple-500 bg-purple-500"
                                        : "border-pink-500 bg-pink-500"
                                    : isDark
                                      ? "border-gray-600 group-hover:border-gray-400"
                                      : "border-gray-300 group-hover:border-gray-400"
                                }
                              `}
                                >
                                  {isSelected && (
                                    <FaCheck className="w-4 h-4 text-white" />
                                  )}
                                </div>
                              </div>
                            </div>

                            {/* Selection Indicator */}
                            {isSelected && (
                              <div
                                className={`absolute top-4 left-4 w-2 h-2 rounded-full animate-pulse ${
                                  categoryColor === "blue"
                                    ? "bg-blue-500"
                                    : categoryColor === "purple"
                                      ? "bg-purple-500"
                                      : "bg-pink-500"
                                }`}
                              ></div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Summary Panel - Right column */}
        <div className="xl:col-span-1">
          <div className="sticky top-30">
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 p-8">
              <h3
                className={`text-2xl font-bold mb-6 ${isDark ? "text-white" : "text-gray-900"}`}
              >
                Service Summary
              </h3>

              {selectedDetails ? (
                <div className="space-y-6">
                  {(() => {
                    const categoryIndex = serviceData.findIndex(
                      cat =>
                        cat.category_id === selectedDetails.category.category_id
                    );
                    const categoryColor = ["blue", "purple", "pink"][
                      categoryIndex % 3
                    ];
                    const IconComponent = [
                      AiOutlineCalendar,
                      FiStar,
                      RiPaintBrushLine,
                    ][categoryIndex % 3];

                    return (
                      <div
                        className={`p-4 rounded-2xl ${
                          categoryColor === "blue"
                            ? "bg-blue-50 dark:bg-blue-900/20"
                            : categoryColor === "purple"
                              ? "bg-purple-50 dark:bg-purple-900/20"
                              : "bg-pink-50 dark:bg-pink-900/20"
                        }`}
                      >
                        <div className="flex items-center space-x-3 mb-3">
                          <div
                            className={`w-8 h-8 rounded-xl flex items-center justify-center ${
                              categoryColor === "blue"
                                ? "bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400"
                                : categoryColor === "purple"
                                  ? "bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400"
                                  : "bg-pink-100 dark:bg-pink-900/30 text-pink-600 dark:text-pink-400"
                            }`}
                          >
                            <IconComponent className="text-lg" />
                          </div>
                          <span
                            className={`text-sm font-medium ${isDark ? "text-gray-400" : "text-gray-600"}`}
                          >
                            {selectedDetails.category.name}
                          </span>
                        </div>
                        <h4
                          className={`font-bold text-xl mb-2 ${isDark ? "text-white" : "text-gray-900"}`}
                        >
                          {selectedDetails.service.name}
                        </h4>
                        <p
                          className={`text-sm mb-4 ${isDark ? "text-gray-400" : "text-gray-600"}`}
                        >
                          {selectedDetails.service.description ||
                            "Professional service"}
                        </p>
                        <div className="flex items-center justify-between">
                          <span
                            className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
                          >
                            Duration: {selectedDetails.service.duration} min
                          </span>
                          <span
                            className={`text-2xl font-bold ${
                              categoryColor === "blue"
                                ? "text-blue-600"
                                : categoryColor === "purple"
                                  ? "text-purple-600"
                                  : "text-pink-600"
                            }`}
                          >
                            ${selectedDetails.service.price}
                          </span>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                    <AiOutlineCalendar
                      className={`text-2xl ${isDark ? "text-gray-500" : "text-gray-400"}`}
                    />
                  </div>
                  <p
                    className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
                  >
                    Select a service to see details
                  </p>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="space-y-4 mt-8">
                <button
                  className={`
                    w-full py-3 px-6 rounded-full font-semibold transition-all duration-200 hover:scale-[1.02]
                    ${
                      isDark
                        ? "bg-gray-700 hover:bg-gray-600 text-white"
                        : "bg-gray-200 hover:bg-gray-300 text-gray-700"
                    }
                  `}
                  onClick={() => setStep(0)}
                >
                  Previous
                </button>

                <ButtonAnimation
                  onClick={() => setStep(2)}
                  disabled={!selectedService}
                  className="w-full"
                >
                  Continue
                </ButtonAnimation>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
