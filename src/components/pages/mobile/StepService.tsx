"use client";
import { useBookingStore } from "@/lib/store/booking";
import { useTheme } from "@/hooks/useTheme";
import { FaCheck } from "react-icons/fa";
import { FiScissors } from "react-icons/fi";
import { getAvailableService } from "@/api/booking";
import { ServiceList, Service, ServiceCategory } from "@/types/index";
import { useMessage } from "@/hooks/useMessage";
import { useEffect, useState, useCallback } from "react";
import { IoWarning } from "react-icons/io5";

export default function StepService() {
  const { data, updateData, setStep } = useBookingStore();
  const { isDark } = useTheme();
  const message = useMessage();
  const [serviceData, setServiceData] = useState<ServiceList[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const selectedService = data.service;

  const getAvailableServiceDetail = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const { data } = await getAvailableService();
      setServiceData(data?.obj || []);
    } catch (err) {
      console.error("getAvailableService error:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load services";
      setError(errorMessage);
      message.error("Failed to load services. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [message]);

  const handleServiceSelect = useCallback(
    (serviceId: string, service: Service, category: ServiceCategory) => {
      updateData({ service: serviceId, serviceDetail: service, category });
    },
    [updateData]
  );

  useEffect(() => {
    getAvailableServiceDetail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2
          className={`text-2xl font-bold mb-2 ${isDark ? "text-white" : "text-gray-900"}`}
        >
          Choose Service
        </h2>
        <p className={`${isDark ? "text-gray-400" : "text-gray-600"}`}>
          Please select the service you need
        </p>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-20">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-10 w-10 border-4 border-blue-500 border-t-transparent mx-auto"></div>
            <p
              className={`text-base ${isDark ? "text-gray-400" : "text-gray-600"}`}
            >
              Loading services...
            </p>
          </div>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center py-20">
          <div className="text-center space-y-4">
            <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center">
              <IoWarning className="w-6 h-6 text-red-500" />
            </div>
            <p
              className={`text-base font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
            >
              Failed to load services
            </p>
            <p
              className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
            >
              {error}
            </p>
          </div>
        </div>
      ) : serviceData.length === 0 ? (
        <div className="flex items-center justify-center py-20">
          <div className="text-center space-y-4">
            <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
              <FiScissors
                className={`text-xl ${isDark ? "text-gray-500" : "text-gray-400"}`}
              />
            </div>
            <p
              className={`text-base ${isDark ? "text-gray-400" : "text-gray-600"}`}
            >
              No services available
            </p>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {serviceData.map(category => (
            <div key={category.category_id} className="space-y-3">
              <h3
                className={`text-xl font-bold ${isDark ? "text-white" : "text-gray-900"}`}
              >
                {category.name}
              </h3>
              <div className="space-y-3">
                {category.services?.map(service => (
                  <div
                    key={service.service_id}
                    className={`
                      relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200
                      ${
                        selectedService === service.service_id
                          ? "border-[#007AFF] bg-blue-50 dark:bg-blue-900/20"
                          : isDark
                            ? "border-gray-600 bg-gray-800 hover:border-gray-500"
                            : "border-gray-200 bg-white hover:border-gray-300"
                      }
                    `}
                    onClick={() =>
                      handleServiceSelect(service.service_id, service, category)
                    }
                  >
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1 min-w-0">
                        <h4
                          className={`font-semibold text-lg break-words leading-tight mb-1 ${isDark ? "text-white" : "text-gray-900"}`}
                        >
                          {service.name}
                        </h4>
                        <p
                          className={`text-sm break-words ${isDark ? "text-gray-400" : "text-gray-600"}`}
                        >
                          Duration: {service.duration} min
                        </p>
                        {service.description && (
                          <p
                            className={`text-xs mt-1 break-words ${isDark ? "text-gray-500" : "text-gray-500"}`}
                          >
                            {service.description}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center space-x-3 flex-shrink-0">
                        <span
                          className={`font-bold text-lg whitespace-nowrap ${
                            selectedService === service.service_id
                              ? "text-[#007AFF]"
                              : isDark
                                ? "text-white"
                                : "text-gray-900"
                          }`}
                        >
                          ${service.price}
                        </span>
                        <div
                          className={`
                          w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors duration-200 flex-shrink-0
                          ${
                            selectedService === service.service_id
                              ? "border-[#007AFF] bg-[#007AFF]"
                              : isDark
                                ? "border-gray-600"
                                : "border-gray-300"
                          }
                        `}
                        >
                          {selectedService === service.service_id && (
                            <FaCheck className="w-3 h-3 text-white" />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="pt-6 flex space-x-4">
        <button
          className={`
            flex-1 py-3 px-6 rounded-xl font-semibold transition-all duration-200
            ${
              isDark
                ? "bg-gray-700 hover:bg-gray-600 text-white"
                : "bg-gray-200 hover:bg-gray-300 text-gray-700"
            }
          `}
          onClick={() => setStep(0)}
        >
          Previous
        </button>
      </div>
      <div className="left-0 px-10 fixed bottom-5 w-full">
        <button
          className={`
            w-full py-3 px-6 rounded-xl font-semibold transition-all duration-200 transform
            shadow-2xl
            ${
              selectedService && !loading
                ? "bg-[#007AFF] hover:bg-[#0051D5] text-white hover:scale-105 active:scale-95"
                : "bg-gray-300 text-gray-500 cursor-not-allowed"
            }
          `}
          disabled={!selectedService || loading}
          onClick={() => setStep(2)}
        >
          {loading ? "Loading..." : "Next"}
        </button>
      </div>
    </div>
  );
}
