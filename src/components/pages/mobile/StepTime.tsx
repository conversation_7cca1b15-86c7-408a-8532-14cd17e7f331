"use client";
import { useBookingStore } from "@/lib/store/booking";
import { useTheme } from "@/hooks/useTheme";
import { Calendar } from "antd-mobile";
import { CiCalendarDate } from "react-icons/ci";
import { IoMdTime } from "react-icons/io";
import React, { useState, useEffect, useCallback } from "react";
import { getAvailableSlots } from "@/api/booking";
import { useMessage } from "@/hooks/useMessage";
import dayjs from "dayjs";
import { convertDisplayTimeToTime } from "@/utils/util";

interface TimeSlot {
  time: string;
  available: boolean;
  display_start_time?: string; // Display time from API
  start_time?: number; // Unix timestamp from API
}

interface ApiTimeSlot {
  display_start_time: string;
  start_time: number;
  available: boolean;
}

interface ApiDayData {
  date: string;
  time_slots: ApiTimeSlot[];
}

export default function StepTime() {
  const { data, updateData, setStep } = useBookingStore();
  const { isDark } = useTheme();
  const message = useMessage();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastFetchedDate, setLastFetchedDate] = useState<string | null>(null);

  const selectedTime = data.time;

  // Get date range for API call (same date for start and end)
  const getDateRange = useCallback((date: Date) => {
    const dateStr = dayjs(date).format("YYYY-MM-DD");
    return {
      start_date: dateStr,
      end_date: dateStr,
    };
  }, []);

  // Fetch available slots for the selected date
  const fetchAvailableSlots = useCallback(
    async (selectedDate: Date) => {
      if (!data.service || !selectedDate) {
        console.warn(
          "No service selected or no date selected, cannot fetch available slots"
        );
        return;
      }

      const dateKey = dayjs(selectedDate).format("YYYY-MM-DD");

      // Prevent duplicate calls for the same date
      if (lastFetchedDate === dateKey || loading) {
        return;
      }

      try {
        setLoading(true);
        setLastFetchedDate(dateKey);
        const { start_date, end_date } = getDateRange(selectedDate);

        console.log(`Fetching slots for date: ${start_date}`);

        const response = await getAvailableSlots({
          service_id: data.service,
          client_count: 1,
          start_date,
          end_date,
        });

        console.log("Available slots response:", response.data);

        // Process the API response to update available slots
        if (
          response.data?.obj &&
          Array.isArray(response.data.obj) &&
          response.data.obj.length > 0
        ) {
          const dayData: ApiDayData = response.data.obj[0]; // Should only have one day's data

          if (dayData.time_slots && Array.isArray(dayData.time_slots)) {
            // Convert API time slots to our format
            const apiSlots: TimeSlot[] = dayData.time_slots.map(apiSlot => ({
              time: convertDisplayTimeToTime(apiSlot.display_start_time),
              available: apiSlot.available,
              start_time: apiSlot.start_time,
              display_start_time: apiSlot.display_start_time,
            }));

            console.log("API slots:", apiSlots);
            setAvailableSlots(apiSlots);
          } else {
            // No time_slots data, use default
            setAvailableSlots([]);
          }
        } else {
          // No data returned, set all slots to available
          setAvailableSlots([]);
        }
      } catch (err) {
        console.error("Failed to fetch available slots:", err);
        message.error("Failed to load available time slots");
        // Fall back to default slots on error
        setAvailableSlots([]);
      } finally {
        setLoading(false);
      }
    },
    [data.service, getDateRange, message, lastFetchedDate, loading]
  );

  // Initialize with today's date if not already set
  useEffect(() => {
    if (!data.date) {
      const today = new Date();
      setSelectedDate(today);
      updateData({ date: today.toISOString() });
    } else {
      const dateFromStore = new Date(data.date);
      setSelectedDate(dateFromStore);
    }
  }, [data.date, updateData]);

  // Fetch slots when service or selected date changes
  useEffect(() => {
    if (data.service && selectedDate) {
      fetchAvailableSlots(selectedDate);
    }
  }, [data.service, selectedDate, fetchAvailableSlots]);

  // Handle calendar date selection
  const handleDateChange = useCallback(
    (val: Date | null) => {
      if (val) {
        console.log("Date selected:", dayjs(val).format("YYYY-MM-DD"));

        setSelectedDate(val);
        updateData({
          date: val.toISOString(),
          time: "",
          scheduled_start_time: undefined,
        });

        // Reset last fetched date to ensure new date gets fresh data
        setLastFetchedDate(null);
      }
    },
    [updateData]
  );

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2
          className={`text-2xl font-bold mb-2 ${isDark ? "text-white" : "text-gray-900"}`}
        >
          Choose Date & Time
        </h2>
        <p className={`${isDark ? "text-gray-400" : "text-gray-600"}`}>
          Please select your preferred appointment date and time
        </p>
      </div>

      {/* Date Selection */}
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <CiCalendarDate
            className={`w-5 h-5 mr-2 ${isDark ? "text-gray-400" : "text-gray-600"}`}
          />
          <span
            className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
          >
            Select Date
          </span>
        </div>
        <div
          className={`rounded-xl border overflow-hidden ${
            isDark ? "border-gray-600 bg-gray-800" : "border-gray-300 bg-white"
          }`}
        >
          <Calendar
            selectionMode="single"
            value={selectedDate}
            onChange={handleDateChange}
            min={new Date()}
            style={
              {
                "--adm-color-primary": "#007AFF",
                backgroundColor: isDark ? "#1f2937" : "#ffffff",
                color: isDark ? "#ffffff" : "#000000",
                borderColor: isDark ? "#374151" : "#e5e7eb",
              } as React.CSSProperties
            }
          />
        </div>
      </div>

      {/* Time Selection */}
      <div>
        <div className="flex items-center mb-4">
          <IoMdTime
            className={`w-5 h-5 mr-2 ${isDark ? "text-gray-400" : "text-gray-600"}`}
          />
          <span
            className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
          >
            Select Time
          </span>
        </div>
        <div className="grid grid-cols-4 gap-3">
          {availableSlots?.map(slot => (
            <button
              key={slot.time}
              disabled={!slot.available || loading}
              className={`
                py-3 px-2 rounded-lg font-semibold text-sm transition-all duration-200
                ${
                  !slot.available
                    ? "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-400 dark:text-gray-600 cursor-not-allowed"
                    : selectedTime === slot.time
                      ? "bg-[#007AFF] text-white shadow-lg"
                      : isDark
                        ? "bg-gray-800 border border-gray-600 text-gray-300 hover:bg-gray-700"
                        : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
                }
              `}
              onClick={() =>
                slot.available &&
                updateData({
                  time: slot.time,
                  scheduled_start_time: slot.start_time,
                })
              }
            >
              <div className="flex flex-col items-center">
                <span>{slot.display_start_time || slot.time}</span>
                {!slot.available && (
                  <span className="text-xs opacity-70">Full</span>
                )}
              </div>
            </button>
          ))}
        </div>
      </div>

      <div className="pt-6 flex space-x-4">
        <button
          className={`
            flex-1 py-3 px-6 rounded-xl font-semibold transition-all duration-200
            ${
              isDark
                ? "bg-gray-700 hover:bg-gray-600 text-white"
                : "bg-gray-200 hover:bg-gray-300 text-gray-700"
            }
          `}
          onClick={() => setStep(1)}
        >
          Previous
        </button>
      </div>
      <div className="left-0 px-10 fixed bottom-5 w-full">
        <button
          className={`
            w-full py-3 px-6 rounded-xl font-semibold transition-all duration-200 transform shadow-2xl
            ${
              selectedTime && selectedDate
                ? "bg-[#007AFF] hover:bg-[#0051D5] text-white hover:scale-105 active:scale-95"
                : "bg-gray-300 text-gray-500 cursor-not-allowed"
            }
          `}
          disabled={!selectedTime || !selectedDate}
          onClick={() => setStep(3)}
        >
          Next
        </button>
      </div>
    </div>
  );
}
