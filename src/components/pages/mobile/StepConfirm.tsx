"use client";
import { useBookingStore } from "@/lib/store/booking";
import { useTheme } from "@/hooks/useTheme";
import { FaCheckCircle, FaRegCommentDots } from "react-icons/fa";
import { FiUser } from "react-icons/fi";
import { CiPhone, CiCalendarDate } from "react-icons/ci";
import { MdOutlineEmail } from "react-icons/md";
import { IoMdTime, IoIosPeople } from "react-icons/io";
import { useRouter } from "next/navigation";
import { submitBooking } from "@/api/booking";
import { useMessage } from "@/hooks/useMessage";
import { useState, useCallback, useEffect } from "react";
import { CiViewList } from "react-icons/ci";
import dayjs from "dayjs";
import { normalizePhoneNumber } from "@/utils/util";
import { IoWarning } from "react-icons/io5";

export default function StepConfirm() {
  const { data, setStep } = useBookingStore();
  const { isDark } = useTheme();
  const router = useRouter();
  const message = useMessage();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const service = {
    service: data.service,
    name: data.serviceDetail?.name,
    price: `$${data.serviceDetail?.price || 0}`,
    duration: `${data.serviceDetail?.duration || 0} min`,
    category: data.category?.name,
    description: data.serviceDetail?.description,
  };

  const selectedDate = data.date ? new Date(data.date) : new Date();

  // Validate booking data
  const validateBookingData = useCallback(() => {
    const errors: string[] = [];

    if (!data.firstName?.trim()) errors.push("First name is required");
    if (!data.lastName?.trim()) errors.push("Last name is required");
    if (!data.tel?.trim()) errors.push("Phone number is required");
    if (!data.service) errors.push("Service selection is required");
    if (!data.date) errors.push("Date selection is required");
    if (!data.time) errors.push("Time selection is required");

    // Validate phone format (basic)
    if (data.tel && !/^[\d\s\-\(\)\+]+$/.test(data.tel)) {
      errors.push("Please enter a valid phone number");
    }

    // Validate email format if provided
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push("Please enter a valid email address");
    }

    setValidationErrors(errors);
    return errors.length === 0;
  }, [data]);

  const handleConfirm = useCallback(async () => {
    // Validate data first
    if (!validateBookingData()) {
      message.error("Please correct the errors before submitting");
      return;
    }

    try {
      setIsSubmitting(true);
      // Prepare booking data for API
      const bookingData = {
        service_id: data.service!,
        client_count: data.numberOfPeople || 1,
        scheduled_start_date: dayjs(data.date).format("YYYY-MM-DD"),
        scheduled_start_time: data.scheduled_start_time as number,
        first_name: data.firstName!.trim(),
        last_name: data.lastName!.trim(),
        phone_number: "+1" + normalizePhoneNumber(data.tel!.trim()),
        note: data?.comments || undefined,
        email: data.email?.trim() || undefined,
      };

      console.log("Submitting booking data:", bookingData);

      const response = await submitBooking(bookingData);

      console.log("Booking submitted successfully:", response.data);

      // Navigate to confirmation page
      router.replace("/booking/confirmed");
    } catch (error: any) {
      console.error("Failed to submit booking:", error);
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to submit booking";
      message.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  }, [data, validateBookingData, message, router]);

  // Validate on component mount and data changes
  useEffect(() => {
    validateBookingData();
  }, [validateBookingData]);

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <FaCheckCircle className="w-16 h-16 text-[#007AFF] mx-auto mb-4" />
        <h2
          className={`text-2xl font-bold mb-2 ${isDark ? "text-white" : "text-gray-900"}`}
        >
          Confirm Booking Information
        </h2>
        <p className={`${isDark ? "text-gray-400" : "text-gray-600"}`}>
          Please review the following information carefully before confirming
          your booking
        </p>
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
          <div className="flex items-start space-x-3">
            <IoWarning className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="font-semibold text-red-800 dark:text-red-300 mb-2 text-sm">
                Please fix the following issues:
              </h3>
              <ul className="space-y-1">
                {validationErrors.map((error, index) => (
                  <li
                    key={index}
                    className="text-xs text-red-700 dark:text-red-200"
                  >
                    • {error}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Booking Information Card */}
      <div
        className={`
        p-6 rounded-2xl border
        ${isDark ? "border-gray-600 bg-gray-800" : "border-gray-200 bg-gray-50"}
      `}
      >
        <div className="space-y-4">
          {/* User Information */}
          <div className="flex items-center space-x-3">
            <FiUser
              className={`w-5 h-5 ${isDark ? "text-gray-400" : "text-gray-500"}`}
            />
            <div>
              <p
                className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
              >
                Name
              </p>
              <p
                className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
              >
                {data.firstName} {data.lastName}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <CiPhone
              className={`w-5 h-5 ${isDark ? "text-gray-400" : "text-gray-500"}`}
            />
            <div>
              <p
                className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
              >
                Phone
              </p>
              <p
                className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
              >
                {data.tel}
              </p>
            </div>
          </div>

          {data.email && (
            <div className="flex items-center space-x-3">
              <MdOutlineEmail
                className={`w-5 h-5 ${isDark ? "text-gray-400" : "text-gray-500"}`}
              />
              <div>
                <p
                  className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
                >
                  Email
                </p>
                <p
                  className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  {data.email}
                </p>
              </div>
            </div>
          )}

          {/* Number of People */}
          <div className="flex items-center space-x-3">
            <IoIosPeople
              className={`w-5 h-5 ${isDark ? "text-gray-400" : "text-gray-500"}`}
            />
            <div>
              <p
                className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
              >
                People
              </p>
              <p
                className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
              >
                {data.numberOfPeople || 1}{" "}
                {(data.numberOfPeople || 1) === 1 ? "Person" : "People"}
              </p>
            </div>
          </div>

          {data.comments && (
            <div className="flex items-start space-x-3">
              <FaRegCommentDots
                className={`w-5 h-5 mt-0.5 ${isDark ? "text-gray-400" : "text-gray-500"}`}
              />
              <div>
                <p
                  className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
                >
                  Comments
                </p>
                <p
                  className={`font-semibold break-all ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  {data.comments}
                </p>
              </div>
            </div>
          )}

          <hr
            className={`border-t ${isDark ? "border-gray-600" : "border-gray-200"}`}
          />

          {/* Service Information */}
          <div>
            <div
              className={`text-sm flex items-center ${isDark ? "text-gray-400" : "text-gray-600"} mb-2`}
            >
              <CiViewList className="w-4 h-4 mr-1" />
              Service
            </div>
            <div className="flex justify-between">
              <div>
                <div
                  className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  {service?.name}
                </div>
                <div
                  className={`text-sm ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  {service.description}
                </div>
                <div
                  className={`text-sm ${isDark ? "text-white" : "text-gray-900"}`}
                >
                  duration: {service.duration}
                </div>
              </div>
              <span className={`font-bold text-lg text-[#007AFF]`}>
                {service?.price}
              </span>
            </div>
          </div>

          <hr
            className={`border-t ${isDark ? "border-gray-600" : "border-gray-200"}`}
          />

          {/* Date & Time Information */}
          <div className="flex items-center space-x-3">
            <CiCalendarDate
              className={`w-5 h-5 ${isDark ? "text-gray-400" : "text-gray-500"}`}
            />
            <div>
              <p
                className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
              >
                Date
              </p>
              <p
                className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
              >
                {selectedDate.toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                  weekday: "long",
                })}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <IoMdTime
              className={`w-5 h-5 ${isDark ? "text-gray-400" : "text-gray-500"}`}
            />
            <div>
              <p
                className={`text-sm ${isDark ? "text-gray-400" : "text-gray-600"}`}
              >
                Time
              </p>
              <p
                className={`font-semibold ${isDark ? "text-white" : "text-gray-900"}`}
              >
                {data.time || "Not selected"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Important Notice */}
      {/* <div
        className={`
        p-4 rounded-xl
        ${
          isDark
            ? "bg-blue-900/20 border border-blue-500/30"
            : "bg-blue-50 border border-blue-200"
        }
      `}
      >
        <p className={`text-sm ${isDark ? "text-blue-300" : "text-blue-700"}`}>
          <strong>Important Notice:</strong>
          After submitting your booking, we will contact you within 30 minutes
          to confirm the appointment details. Please contact us promptly if you
          need to make any changes.
        </p>
      </div> */}

      <div className="pt-6 flex space-x-4">
        <button
          className={`
            flex-1 py-3 px-6 rounded-xl font-semibold transition-all duration-200
            ${
              isDark
                ? "bg-gray-700 hover:bg-gray-600 text-white"
                : "bg-gray-200 hover:bg-gray-300 text-gray-700"
            }
          `}
          onClick={() => setStep(2)}
        >
          Previous
        </button>
      </div>
      <div className="left-0 px-10 fixed bottom-5 w-full">
        <button
          className={`
            w-full py-3 px-6 rounded-xl font-semibold transition-all duration-200 transform shadow-2xl
            ${
              isSubmitting || validationErrors.length > 0
                ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                : "bg-[#007AFF] hover:bg-[#0051D5] text-white hover:scale-105 active:scale-95"
            }
          `}
          onClick={handleConfirm}
          disabled={isSubmitting || validationErrors.length > 0}
        >
          {isSubmitting ? "Submitting..." : "Confirm"}
        </button>
      </div>
    </div>
  );
}
