"use client";
import { IoMdTime } from "react-icons/io";
import { CiPhone } from "react-icons/ci";
import { MdOutlineEmail } from "react-icons/md";
import { SlLocationPin } from "react-icons/sl";
import GoogleMap from "@/components/GoogleMap";
import { useRouter } from "next/navigation";
import { FaAngleUp, FaInfoCircle } from "react-icons/fa";
import { TbExternalLink } from "react-icons/tb";
import { useState, useMemo } from "react";
import ButtonAnimation from "@/components/ButtonAnimation";
import { BusinessDetails } from "@/types";
import { filterBusinessHours, getCurrentStatus } from "@/utils/businessHours";
import BusinessLogo from "@/components/BusinessLogo";
import BannerImage from "@/components/BannerImage";
import {
  formatLocation,
  formatPhoneNumberForDisplayNumber,
} from "@/utils/util";

interface IProps {
  businessDetails?: BusinessDetails;
  loading?: boolean;
}

export default function DesktopHomePage(props: IProps) {
  const { businessDetails } = props;
  const router = useRouter();
  const [isShowOpeningHours, setShowOpeningHours] = useState(false);

  const handleShowOpeningHours = () => {
    setShowOpeningHours(prev => !prev);
  };

  const addressDetail = useMemo(() => {
    return businessDetails?.location
      ? formatLocation(businessDetails?.location)
      : "";
  }, [businessDetails?.location]);

  const filteredBusinessHours = useMemo(() => {
    if (!businessDetails?.business_hours) return [];

    const today = new Date().getDay(); // 0-6, Sunday to Saturday
    return filterBusinessHours(
      businessDetails.business_hours,
      today,
      businessDetails.timezone
    );
  }, [businessDetails?.business_hours, businessDetails?.timezone]);

  const currentStatus = useMemo(() => {
    if (!businessDetails?.business_hours) return "Closed";

    const today = new Date().getDay();
    return getCurrentStatus(
      businessDetails.business_hours,
      today,
      businessDetails.timezone
    );
  }, [businessDetails?.business_hours, businessDetails?.timezone]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      {/* Main Container */}
      <div className="max-w-7xl mx-auto px-8 py-12">
        <div className="grid grid-cols-1 xl:grid-cols-5 gap-8 lg:gap-12">
          {/* Left Column - Hero Section */}
          <div className="xl:col-span-3 space-y-10">
            {/* Hero Card */}
            <div className="relative overflow-hidden bg-white dark:bg-gray-800 backdrop-blur-xl rounded-[2.5rem] shadow-2xl shadow-purple-500/10 dark:shadow-purple-500/5 border border-gray-100 dark:border-gray-700/50">
              {/* Background Gradient */}
              <div className="absolute inset-0"></div>

              {/* Hero Image Area */}
              <div className="relative h-80 lg:h-96">
                <BannerImage
                  bannerUrl={businessDetails?.brand_image}
                  className="rounded-br-[120px]"
                />

                {/* Logo Container */}
                <div className="absolute -bottom-14 left-12 right-0">
                  <div className="w-24 h-24 lg:w-28 lg:h-28 rounded-3xl p-2 bg-white dark:bg-gray-800">
                    <BusinessLogo
                      businessName={businessDetails?.business_name}
                      logoUrl={businessDetails?.brand_logo}
                      size="full"
                    />
                  </div>
                </div>
              </div>

              {/* Content Area */}
              <div className="relative p-8 lg:p-12 mt-10">
                <h1 className="text-5xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-200 bg-clip-text text-transparent leading-tight">
                  {businessDetails?.business_name}
                </h1>
                <p className="text-xl lg:text-2xl mb-10 font-medium leading-relaxed text-gray-600 dark:text-gray-300 max-w-2xl">
                  {businessDetails?.about_us}
                </p>

                {/* CTA Button */}
                <ButtonAnimation onClick={() => router.push("/booking")}>
                  Book Now
                </ButtonAnimation>
              </div>
            </div>
          </div>

          {/* Right Column - Contact Info & Map */}
          <div className="xl:col-span-2 space-y-8">
            {/* Contact Information Card */}
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 overflow-hidden">
              <div className="p-8">
                <div className="flex items-center space-x-3 mb-8">
                  <div className="w-10 h-10 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                    <FaInfoCircle className="w-5 h-5 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Contact Information
                  </h2>
                </div>

                <div className="space-y-6">
                  {/* Opening Hours */}
                  <div className="group">
                    <div
                      className="flex items-center justify-between p-4 rounded-2xl cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700/50"
                      onClick={handleShowOpeningHours}
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-xl bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                          <IoMdTime className="text-blue-600 dark:text-blue-400 text-lg" />
                        </div>
                        <div className="select-none">
                          <div className="font-semibold text-gray-900 dark:text-white">
                            Opening Hours
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {currentStatus}
                          </div>
                        </div>
                      </div>
                      <FaAngleUp
                        className={`text-gray-400 transition-transform duration-200 ${isShowOpeningHours && "rotate-180"}`}
                      />
                    </div>
                    {isShowOpeningHours && (
                      <div className="mt-4 ml-14 space-y-3 text-sm transition-all duration-200 ease-in-out select-none">
                        {filteredBusinessHours.map(
                          ({ day, hours, isToday, isClosed }) => (
                            <div
                              key={day}
                              className={`flex justify-between py-2 px-3 rounded-lg ${
                                isToday
                                  ? "bg-purple-50 dark:bg-purple-900/20 text-purple-900 dark:text-purple-200 font-medium"
                                  : isClosed
                                    ? "text-gray-400 dark:text-gray-500"
                                    : "text-gray-600 dark:text-gray-400"
                              }`}
                            >
                              <span>{day}</span>
                              <span className={isToday ? "font-semibold" : ""}>
                                {hours}
                              </span>
                            </div>
                          )
                        )}
                        {businessDetails?.timezone && (
                          <div className="text-center text-gray-500 dark:text-gray-400">
                            Time zone ({businessDetails.timezone})
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Contact Items */}
                  {[
                    {
                      icon: CiPhone,
                      title: "Phone",
                      content:
                        businessDetails?.phone_number &&
                        formatPhoneNumberForDisplayNumber(
                          businessDetails?.phone_number
                        ),
                      href: `tel:${businessDetails?.phone_number}`,
                      bgColor: "bg-green-100 dark:bg-green-900/30",
                      iconColor: "text-green-600 dark:text-green-400",
                    },
                    {
                      icon: MdOutlineEmail,
                      title: "Email",
                      content: businessDetails?.email,
                      href: `mailto:${businessDetails?.email}`,
                      bgColor: "bg-red-100 dark:bg-red-900/30",
                      iconColor: "text-red-600 dark:text-red-400",
                    },
                    {
                      icon: SlLocationPin,
                      title: "Address",
                      content: addressDetail,
                      href: `https://www.google.com/maps?q=${addressDetail}`,
                      bgColor: "bg-blue-100 dark:bg-blue-900/30",
                      iconColor: "text-blue-600 dark:text-blue-400",
                    },
                  ].map(
                    ({
                      icon: Icon,
                      title,
                      content,
                      href,
                      bgColor,
                      iconColor,
                    }) => (
                      <a
                        key={title}
                        href={href}
                        target={title === "Address" ? "_blank" : undefined}
                        rel={
                          title === "Address"
                            ? "noopener noreferrer"
                            : undefined
                        }
                        className="group flex items-center space-x-4 p-4 rounded-2xl transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:scale-[1.02]"
                      >
                        <div
                          className={`w-10 h-10 rounded-xl flex items-center justify-center ${bgColor}`}
                        >
                          <Icon className={`${iconColor} text-lg`} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-semibold text-gray-900 dark:text-white">
                            {title}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400 truncate group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                            {content}
                          </div>
                        </div>
                        <TbExternalLink className="w-5 h-5 text-gray-400 group-hover:text-purple-500 transition-colors opacity-0 group-hover:opacity-100" />
                      </a>
                    )
                  )}
                </div>
              </div>
            </div>

            {/* Map Card */}
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl border border-gray-100 dark:border-gray-700/50 shadow-xl shadow-gray-500/5 dark:shadow-black/10 overflow-hidden">
              <div className="p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-r from-blue-500 to-teal-500 flex items-center justify-center">
                    <SlLocationPin className="text-white text-sm" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Location
                  </h3>
                </div>
              </div>
              <div className="aspect-[4/3] overflow-hidden">
                <GoogleMap />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
