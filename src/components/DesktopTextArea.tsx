"use client";
import React from "react";
import { Input } from "antd";
import { TextAreaProps } from "antd/es/input";
import { useTheme } from "@/hooks/useTheme";

const { TextArea } = Input;

interface DesktopTextAreaProps extends TextAreaProps {
  label?: string;
  icon?: React.ReactNode;
  error?: string;
  required?: boolean;
  iconColor?: string;
  showCharCount?: boolean;
  maxCharCount?: number;
}

export default function DesktopTextArea({
  label,
  icon,
  error,
  required = false,
  iconColor = "blue",
  showCharCount = false,
  maxCharCount,
  className = "",
  value,
  ...props
}: DesktopTextAreaProps) {
  const { isDark } = useTheme();

  const getIconColorClasses = (color: string) => {
    const colors = {
      blue: "bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400",
      green: "bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400",
      purple: "bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400",
      red: "bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400",
      orange: "bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400",
      teal: "bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400",
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const textareaClassName = `
    w-full px-5 py-4 rounded-2xl border transition-all duration-200 resize-none text-base
    ${
      error
        ? "border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
        : isDark
          ? "bg-[#1C1C1E] border-[#38383A] text-[#FFFFFF] placeholder-[#8E8E93] focus:border-[#007AFF] focus:ring-2 focus:ring-[#007AFF]/20"
          : "bg-[#FFFFFF] border-[#D1D1D6] text-[#1D1D1F] placeholder-[#8E8E93] focus:border-[#007AFF] focus:ring-2 focus:ring-[#007AFF]/20"
    }
    focus:outline-none hover:shadow-md
    ${className}
  `;

  const currentLength = typeof value === 'string' ? value.length : 0;

  return (
    <div>
      {/* Label with Icon */}
      {(label || icon) && (
        <div className="flex items-center mb-4">
          {icon && (
            <div className={`w-10 h-10 rounded-xl flex items-center justify-center mr-3 ${getIconColorClasses(iconColor)}`}>
              {icon}
            </div>
          )}
          {label && (
            <label className={`text-base font-semibold ${isDark ? "text-[#FFFFFF]" : "text-[#1D1D1F]"}`}>
              {label} {required && "*"}
            </label>
          )}
        </div>
      )}

      {/* Antd TextArea with Custom Styling */}
      <TextArea
        {...props}
        value={value}
        className={textareaClassName}
        style={{
          backgroundColor: error 
            ? undefined 
            : isDark 
              ? '#1C1C1E' 
              : '#FFFFFF',
          borderColor: error
            ? '#ef4444'
            : isDark
              ? '#38383A'
              : '#D1D1D6',
          color: isDark ? '#FFFFFF' : '#1D1D1F',
          resize: 'none',
        }}
      />

      {/* Error and Character Count */}
      <div className="flex justify-between items-center mt-2">
        {error ? (
          <p className="text-red-500 text-sm ml-1">{error}</p>
        ) : (
          <div></div>
        )}
        {showCharCount && maxCharCount && (
          <p className={`text-sm ${isDark ? "text-[#8E8E93]" : "text-[#8E8E93]"}`}>
            {currentLength}/{maxCharCount}
          </p>
        )}
      </div>
    </div>
  );
}