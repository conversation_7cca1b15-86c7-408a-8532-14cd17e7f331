import { create } from "zustand";
import { Service, ServiceCategory } from "@/types/index";

type BookingData = {
  firstName: string;
  lastName: string;
  tel: string;
  email: string;
  comments: string;
  service: string;
  date: string;
  time: string;
  numberOfPeople: number;
  scheduled_start_time: number;
  serviceDetail: Service;
  category: ServiceCategory;
};

type BookingState = {
  step: number;
  data: Partial<BookingData>;
  setStep: (step: number) => void;
  updateData: (values: Partial<BookingData>) => void;
  reset: () => void;
};

export const useBookingStore = create<BookingState>(set => ({
  step: 0,
  data: {},
  setStep: step => set({ step }),
  updateData: values => set(state => ({ data: { ...state.data, ...values } })),
  reset: () => set({ step: 0, data: {} }),
}));
