# ========= build =========
FROM node:20-bullseye-slim AS builder
WORKDIR /app

# 关掉遥测（可选）
ENV NEXT_TELEMETRY_DISABLED=1

# 先装依赖再拷源码，利用缓存
COPY package*.json ./
RUN npm ci

# 拷贝全部源码（包含 src/app 等）
COPY . .
# 建议在 next.config.ts 设置：export default { output: 'standalone' }
RUN npm run build

# ========= run =========
FROM node:20-bullseye-slim AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV HOSTNAME=0.0.0.0

# 只拷运行必需：standalone + 静态 + public
COPY --from=builder /app/.next/standalone ./standalone
COPY --from=builder /app/.next/static ./standalone/.next/static
COPY --from=builder /app/public ./standalone/public

EXPOSE ${PORT:-9000}
CMD ["sh", "-c", "node standalone/server.js -p ${PORT:-9000}"]

# 如果没用 standalone，可改为以下（注释上面3行）：
# COPY --from=builder /app/package*.json ./
# RUN npm ci --omit=dev
# COPY --from=builder /app/.next ./.next
# EXPOSE 9000
# CMD ["npm","run","start","--silent"]

