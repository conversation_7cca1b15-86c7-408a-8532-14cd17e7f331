{"name": "booking", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"@ant-design/nextjs-registry": "^1.1.0", "@vis.gl/react-google-maps": "^1.5.5", "antd": "^5.27.1", "antd-mobile": "^5.39.0", "axios": "^1.11.0", "next": "15.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "ua-parser-js": "^2.0.4", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^18.3.24", "@types/react-dom": "^18.3.7", "eslint": "^9", "eslint-config-next": "15.5.0", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5"}}