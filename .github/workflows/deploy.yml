name: Deploy to Multi-Environment

on:
  push:
    branches: [main, staging]
  workflow_dispatch:

# ========================================
# 🔧 环境配置常量 - 一键修改区域
# ========================================
env:
  # 容器镜像配置
  REGISTRY: 283466880333.dkr.ecr.us-east-1.amazonaws.com
  IMAGE_NAME: pebbble/booking-web
  ECR_REGION: us-east-1
  
  # 分支环境映射配置 (一键修改)
  PRODUCTION_BRANCH: "main"        # 正式环境分支
  STAGING_BRANCH: "staging"        # 测试环境分支
  
  # 服务器配置
  PROD_HOST_SECRET: "PROD_EC2_HOST"
  PROD_USER_SECRET: "PROD_EC2_USERNAME" 
  PROD_KEY_SECRET: "PROD_EC2_SSH_PRIVATE_KEY"
  
  STAGING_HOST_SECRET: "STAGING_EC2_HOST"
  STAGING_USER_SECRET: "STAGING_EC2_USERNAME"
  STAGING_KEY_SECRET: "STAGING_EC2_SSH_PRIVATE_KEY"
  
  # 部署路径配置
  PROD_DEPLOY_PATH: "/home/<USER>/application/booking-prod"
  STAGING_DEPLOY_PATH: "/home/<USER>/application/booking-staging"
  
  # 容器端口配置
  PROD_PORT: "9000"
  STAGING_PORT: "9001"

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name == 'main' && 'production' || 'staging' }}
    permissions:
      contents: read
      packages: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # 环境检测和配置设置
      - name: Set environment variables
        id: env
        run: |
          if [ "${{ github.ref_name }}" = "${{ env.PRODUCTION_BRANCH }}" ]; then
            echo "ENVIRONMENT=production" >> $GITHUB_OUTPUT
            echo "EC2_HOST_SECRET=${{ env.PROD_HOST_SECRET }}" >> $GITHUB_OUTPUT
            echo "EC2_USER_SECRET=${{ env.PROD_USER_SECRET }}" >> $GITHUB_OUTPUT
            echo "EC2_KEY_SECRET=${{ env.PROD_KEY_SECRET }}" >> $GITHUB_OUTPUT
            echo "DEPLOY_PATH=${{ env.PROD_DEPLOY_PATH }}" >> $GITHUB_OUTPUT
            echo "APP_PORT=${{ env.PROD_PORT }}" >> $GITHUB_OUTPUT
            echo "CONTAINER_NAME=booking-web-prod" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref_name }}" = "${{ env.STAGING_BRANCH }}" ]; then
            echo "ENVIRONMENT=staging" >> $GITHUB_OUTPUT
            echo "EC2_HOST_SECRET=${{ env.STAGING_HOST_SECRET }}" >> $GITHUB_OUTPUT
            echo "EC2_USER_SECRET=${{ env.STAGING_USER_SECRET }}" >> $GITHUB_OUTPUT
            echo "EC2_KEY_SECRET=${{ env.STAGING_KEY_SECRET }}" >> $GITHUB_OUTPUT
            echo "DEPLOY_PATH=${{ env.STAGING_DEPLOY_PATH }}" >> $GITHUB_OUTPUT
            echo "APP_PORT=${{ env.STAGING_PORT }}" >> $GITHUB_OUTPUT
            echo "CONTAINER_NAME=booking-web-staging" >> $GITHUB_OUTPUT
          else
            echo "❌ Unknown branch: ${{ github.ref_name }}"
            exit 1
          fi

      - name: Generate dynamic environment variables
        id: dynamic-env
        run: |
          ENV_ARGS=""
          
          # 处理所有 Environment Variables (vars.*) - 全部传递
          VARS_JSON='${{ toJson(vars) }}'
          if [ "$VARS_JSON" != "null" ] && [ "$VARS_JSON" != "{}" ]; then
            ENV_ARGS="$ENV_ARGS $(echo "$VARS_JSON" | jq -r 'to_entries[] | "-e " + .key + "=" + .value' | tr '\n' ' ')"
          fi
          
          # 处理 Environment Secrets - 只传递 APP_ 前缀的
          SECRETS_JSON='${{ toJson(secrets) }}'
          if [ "$SECRETS_JSON" != "null" ] && [ "$SECRETS_JSON" != "{}" ]; then
            ENV_ARGS="$ENV_ARGS $(echo "$SECRETS_JSON" | jq -r 'to_entries[] | select(.key | startswith("APP_")) | "-e " + .key + "=" + .value' | tr '\n' ' ')"
          fi
          
          echo "DOCKER_ENV_VARS=$ENV_ARGS" >> $GITHUB_OUTPUT
          echo "🔧 Generated dynamic environment variables for ${{ github.ref_name }} environment"
          echo "ℹ️ Only APP_ prefixed secrets are passed to container for security"

      - name: Display deployment info
        run: |
          echo "🚀 Deploying to: ${{ steps.env.outputs.ENVIRONMENT }}"
          echo "📂 Deploy path: ${{ steps.env.outputs.DEPLOY_PATH }}"
          echo "🔌 Port: ${{ vars.PORT || steps.env.outputs.APP_PORT }}"
          echo "📦 Container: ${{ steps.env.outputs.CONTAINER_NAME }}"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.ECR_REGION }}

      - name: Log in to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=raw,value=${{ steps.env.outputs.ENVIRONMENT }}-latest
            type=sha,prefix=${{ steps.env.outputs.ENVIRONMENT }}-

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Deploy to EC2
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets[steps.env.outputs.EC2_HOST_SECRET] }}
          username: ${{ secrets[steps.env.outputs.EC2_USER_SECRET] }}
          key: ${{ secrets[steps.env.outputs.EC2_KEY_SECRET] }}
          script: |
            # 设置 AWS 凭证环境变量，用于 ECR 访问
            export AWS_ACCESS_KEY_ID="${{ secrets.AWS_ACCESS_KEY_ID }}"
            export AWS_SECRET_ACCESS_KEY="${{ secrets.AWS_SECRET_ACCESS_KEY }}"
            export AWS_DEFAULT_REGION="${{ env.ECR_REGION }}"
            
            # Login to AWS ECR
            aws ecr get-login-password --region ${{ env.ECR_REGION }} | docker login --username AWS --password-stdin ${{ env.REGISTRY }}
            
            # Create and navigate to app directory
            mkdir -p ${{ steps.env.outputs.DEPLOY_PATH }}
            cd ${{ steps.env.outputs.DEPLOY_PATH }}
            
            # Set environment variables
            export ENVIRONMENT=${{ steps.env.outputs.ENVIRONMENT }}
            export APP_PORT=${{ steps.env.outputs.APP_PORT }}
            export CONTAINER_NAME=${{ steps.env.outputs.CONTAINER_NAME }}
            
            # 从 metadata action 输出中提取正确的环境标签
            export IMAGE_TAG=$(echo '${{ steps.meta.outputs.tags }}' | grep -E '${{ steps.env.outputs.ENVIRONMENT }}-latest$' | head -n1)
            
            # 验证镜像标签
            if [ -z "$IMAGE_TAG" ]; then
              echo "❌ Failed to determine image tag"
              echo "📋 Available tags from build:"
              echo '${{ steps.meta.outputs.tags }}'
              exit 1
            fi
            
            echo "🚀 Starting deployment for $ENVIRONMENT environment..."
            echo "📦 Container: $CONTAINER_NAME"
            echo "🔌 Port: $APP_PORT"
            echo "🎯 Selected image tag: $IMAGE_TAG"
            echo "📋 All available tags:"
            echo '${{ steps.meta.outputs.tags }}'
            
            # Create Docker network if not exists
            echo "🌐 Creating Docker network..."
            docker network create booking-network-$ENVIRONMENT --driver bridge || echo "ℹ️ Network already exists"
            
            # Stop existing containers gracefully
            echo "🛑 Stopping existing containers..."
            if docker ps -q --filter "name=$CONTAINER_NAME" | grep -q .; then
                docker stop $CONTAINER_NAME || echo "⚠️ Failed to stop container gracefully"
                docker rm $CONTAINER_NAME || echo "⚠️ Failed to remove container"
            else
                echo "ℹ️ No existing containers found"
            fi
            
            # Pull latest image
            echo "📥 Pulling latest image: $IMAGE_TAG"
            docker pull $IMAGE_TAG
            
            # Start new container
            echo "▶️ Starting new container..."
            docker run -d \
              --name $CONTAINER_NAME \
              -p ${{ vars.PORT || steps.env.outputs.APP_PORT }}:${{ vars.PORT || steps.env.outputs.APP_PORT }} \
              ${{ steps.dynamic-env.outputs.DOCKER_ENV_VARS }} \
              -e ENVIRONMENT=$ENVIRONMENT \
              -e PORT=${{ vars.PORT || steps.env.outputs.APP_PORT }} \
              --restart unless-stopped \
              --network booking-network-$ENVIRONMENT \
              --label "booking.environment=$ENVIRONMENT" \
              --label "booking.port=${{ vars.PORT || steps.env.outputs.APP_PORT }}" \
              $IMAGE_TAG
            
            # Wait and perform health check
            echo "⏳ Waiting for container to be ready..."
            sleep 10
            
            max_attempts=12
            attempt=1
            while [ $attempt -le $max_attempts ]; do
                if docker ps --filter "name=$CONTAINER_NAME" --filter "status=running" | grep -q $CONTAINER_NAME; then
                    echo "✅ Container is running!"
                    if curl -f -s http://localhost:${{ vars.PORT || steps.env.outputs.APP_PORT }}/health > /dev/null; then
                        echo "✅ Application is responding on port ${{ vars.PORT || steps.env.outputs.APP_PORT }}"
                        break
                    else
                        echo "⚠️ Application not yet responding on port ${{ vars.PORT || steps.env.outputs.APP_PORT }} (attempt $attempt/$max_attempts)"
                    fi
                else
                    echo "⚠️ Container not running yet (attempt $attempt/$max_attempts)"
                fi
                
                if [ $attempt -eq $max_attempts ]; then
                    echo "❌ Health check failed after $max_attempts attempts"
                    echo "📋 Container logs:"
                    docker logs $CONTAINER_NAME --tail 20
                    exit 1
                fi
                
                sleep 5
                attempt=$((attempt + 1))
            done
            
            # Display deployment status
            echo "🎉 Deployment completed successfully!"
            echo "🏷️ Environment: $ENVIRONMENT"
            echo "📦 Container: $CONTAINER_NAME"
            echo "🔌 Port: ${{ vars.PORT || steps.env.outputs.APP_PORT }}"
            echo "🎯 Image: $IMAGE_TAG"
            echo ""
            echo "📊 Container status:"
            docker ps --filter "name=$CONTAINER_NAME"
            echo ""
            echo "📝 Recent logs:"
            docker logs $CONTAINER_NAME --tail 10
            
            # Cleanup old images
            echo "🧹 Cleaning up old images..."
            docker system prune -af --volumes